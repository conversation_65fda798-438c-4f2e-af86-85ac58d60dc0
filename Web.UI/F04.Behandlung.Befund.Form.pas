﻿unit F04.Behandlung.Befund.Form;

interface

uses
  System.SysUtils, System.Classes,
  Web,

  Vcl.Controls,

  WEBLib.Graphics, WEBLib.Controls,
  WEBLib.Forms, WEBLib.Dialogs,  WEBLib.WebCtrls,

  Stammdaten.Patient;

type
  TFormBehandlungBefund = class(TWebForm)
    ButtonTabUebersicht: TWebHTMLDiv;
    procedure ButtonTabUebersichtClick(Sender: TObject);
  private
    FPatient: TPatient;
    procedure SetPatient(const Value: TPatient);
    { Private declarations }
  public
    property Patient: TPatient read FPatient write SetPatient;
    { Public declarations }
  end;


implementation

{$R *.dfm}

uses App.Controller;

procedure TFormBehandlungBefund.ButtonTabUebersichtClick(Sender: TObject);
begin
  AppController.ShowFormUebersicht(Patient);
  Close;
end;

procedure TFormBehandlungBefund.SetPatient(const Value: TPatient);
begin
  FPatient := Value;


 // LabelPatient.Caption := FPatient.Name + ', ' + FPatient.VorName;
end;

end.
