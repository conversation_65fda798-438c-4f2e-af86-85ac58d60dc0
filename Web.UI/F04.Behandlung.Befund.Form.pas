﻿unit F04.Behandlung.Befund.Form;

interface

uses
  System.SysUtils, System.Classes,
  Web,

  Vcl.Controls,

  WEBLib.Graphics, WEBLib.Controls,
  WEBLib.Forms, WEBLib.Dialogs,  WEBLib.WebCtrls,

  Stammdaten.Patient;

type
  TFormBehandlungBefund = class(TWebForm)
    ButtonTabUebersicht: TWebHTMLDiv;
    procedure ButtonTabUebersichtClick(Sender: TObject);
  private
    FPatientInfo: TPatientInfo;
    procedure SetPatientInfo(const Value: TPatientInfo);
    { Private declarations }
  public
    property PatientInfo: TPatientInfo read FPatientInfo write SetPatientInfo;
    { Public declarations }
  end;


implementation

{$R *.dfm}

uses App.Controller;

procedure TFormBehandlungBefund.ButtonTabUebersichtClick(Sender: TObject);
begin
  AppController.ShowFormUebersicht(PatientInfo);
  Close;
end;

procedure TFormBehandlungBefund.SetPatientInfo(const Value: TPatientInfo);
begin
  FPatientInfo := Value;

  if Assigned(FPatientInfo) then
  begin
    // LabelPatient.Caption := FPatientInfo.Patient.Name + ', ' + FPatientInfo.Patient.VorName;
  end;
end;

end.
