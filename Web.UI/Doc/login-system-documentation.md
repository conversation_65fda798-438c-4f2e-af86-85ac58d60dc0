# EVIDENT Workspaces Login System Documentation

## Overview
The EVIDENT Workspaces application uses a token-based authentication system. Users provide credentials through a login form, which are validated against a backend service. Upon successful authentication, a JWT token is issued and stored for the session.

## Components

### Login Form
- Located in `F00.Login.Form.pas`
- Collects username and password
- Handles login button clicks and Enter key presses
- Displays error messages for failed login attempts

### Authentication Process
1. User enters credentials in the login form
2. Credentials are passed to `DMMain.Login` method
3. Backend API call is made to `ILoginService.Login`
4. On success:
   - JWT token is stored in memory
   - User is redirected to the waiting room
5. On failure:
   - Error message is displayed
   - Focus returns to password field

### Token Management
- Token is stored in `FToken` variable in `DMMain`
- Added to all API requests via `Authorization: Bearer` header
- No persistent storage (token is lost on page refresh)

### Debug Features
- Auto-login in debug mode using test/test credentials
- Test button visible only in debug builds

## Code Examples

### Login Button Click Handler
```pascal
procedure TFormLogin.ButtonLoginClick(Sender: TObject);
begin
  inherited;
  try
    await(DMMain.Login(EditUser.Text, EditPassword.Text));
    AppController.ShowWartezimmer;
    close;
  except
    MessageDlg('Login fehlerhaft', mtError, [mbOK],
      procedure(AValue: TModalResult)
      begin
        EditPassword.SetFocus;
      end);
  end;
end;
```

### Backend Authentication Call
```pascal
procedure TDMMain.Login(const AUser: string; const APassword: string);
var
  LResponse: TXDataClientResponse;
begin
  LResponse := await(XDataWebClient.RawInvokeAsync('ILoginService.Login', [AUser, APassword]));
  if LResponse.StatusCode = 200 then
  begin
    FToken := string(TJSObject(LResponse.Result)['value']);
    DXLog('User [%s] logged in!', [AUser]);
  end
  else
  begin
    raise Exception.Create('Login ungültig');
  end;
end;
```

### Token Usage in API Requests
```pascal
procedure TDMMain.XDataWebClientRequest(Request: TXDataClientRequest);
begin
  Request.Request.Headers.SetValue('Authorization', 'Bearer ' + FToken);
end;
```