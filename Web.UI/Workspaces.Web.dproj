﻿<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
    <PropertyGroup>
        <Base>True</Base>
        <AppType>Application</AppType>
        <Config Condition="'$(Config)'==''">Debug</Config>
        <FrameworkType>VCL</FrameworkType>
        <MainSource>Workspaces.Web.dpr</MainSource>
        <Platform Condition="'$(Platform)'==''">Win32</Platform>
        <ProjectGuid>{B38CD24B-93CB-4F8C-938F-BFF22B3263EC}</ProjectGuid>
        <ProjectName Condition="'$(ProjectName)'==''">Workspaces.Web</ProjectName>
        <ProjectVersion>20.3</ProjectVersion>
        <TargetedPlatforms>1</TargetedPlatforms>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Base' or '$(Base)'!=''">
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Base)'=='true') or '$(Base_Win32)'!=''">
        <Base_Win32>true</Base_Win32>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Debug' or '$(Cfg_1)'!=''">
        <Cfg_1>true</Cfg_1>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Cfg_1)'=='true') or '$(Cfg_1_Win32)'!=''">
        <Cfg_1_Win32>true</Cfg_1_Win32>
        <CfgParent>Cfg_1</CfgParent>
        <Cfg_1>true</Cfg_1>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Release' or '$(Cfg_2)'!=''">
        <Cfg_2>true</Cfg_2>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Cfg_2)'=='true') or '$(Cfg_2_Win32)'!=''">
        <Cfg_2_Win32>true</Cfg_2_Win32>
        <CfgParent>Cfg_2</CfgParent>
        <Cfg_2>true</Cfg_2>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base)'!=''">
        <SanitizedProjectName>Workspaces_Web</SanitizedProjectName>
        <DCC_DcuOutput>.\$(Platform)\$(Config)</DCC_DcuOutput>
        <DCC_ExeOutput>.\$(Platform)\$(Config)</DCC_ExeOutput>
        <DCC_Namespace>System;Xml;Data;Datasnap;Web;Soap;Vcl;Vcl.Imaging;Vcl.Touch;Vcl.Samples;Vcl.Shell;$(DCC_Namespace)</DCC_Namespace>
        <DCC_UnitSearchPath>.\libs\DX-Library;$(DCC_UnitSearchPath)</DCC_UnitSearchPath>
        <FeatureFilesAdded>SQLite</FeatureFilesAdded>
        <Icns_MainIcns>$(BDS)\bin\delphi_PROJECTICNS.icns</Icns_MainIcns>
        <Icon_MainIcon>$(BDS)\bin\delphi_PROJECTICON.ico</Icon_MainIcon>
        <TMSApplicationDescription>EVIDENT - Software für 18..48</TMSApplicationDescription>
        <TMSPWAApplicationName>EVIDENT Workspaces</TMSPWAApplicationName>
        <TMSPWAApplicationShortName>Workspaces</TMSPWAApplicationShortName>
        <TMSPWAManifestFile>Manifest.json</TMSPWAManifestFile>
        <TMSURL>http://localhost:80/WorkspacesUI</TMSURL>
        <TMSVersion>0.1.808</TMSVersion>
        <TMSVersionAuto>2</TMSVersionAuto>
        <TMSWebHTMLFile>index.html</TMSWebHTMLFile>
        <TMSWebOutputPath>.\Web\$(config)</TMSWebOutputPath>
        <TMSWebPWA>2</TMSWebPWA>
        <TMSWebPWAResIconHighFile>assets\WorkspacesLogo_512x512.png</TMSWebPWAResIconHighFile>
        <TMSWebPWAResIconLowFile>assets\WorkspacesLogo_64x64.png</TMSWebPWAResIconLowFile>
        <TMSWebPWAResIconMidFile>assets\WorkspacesLogo_256x256.png</TMSWebPWAResIconMidFile>
        <TMSWebPWAServiceWorkerFile>serviceworker.js</TMSWebPWAServiceWorkerFile>
        <TMSWebProject>2</TMSWebProject>
        <TMSWebServerParams>-s http://localhost:80/WorkspacesUI $(OutputDir)</TMSWebServerParams>
        <TMSWebSourcePaths>$(TMSWebSourcePaths);.\libs\DX-Library</TMSWebSourcePaths>
        <VerInfo_Keys>CompanyName=;FileDescription=$(MSBuildProjectName);FileVersion=*******;InternalName=;LegalCopyright=;LegalTrademarks=;OriginalFilename=;ProgramID=com.embarcadero.$(MSBuildProjectName);ProductName=$(MSBuildProjectName);ProductVersion=*******;Comments=</VerInfo_Keys>
        <VerInfo_Locale>1031</VerInfo_Locale>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base_Win32)'!=''">
        <AppDPIAwarenessMode>none</AppDPIAwarenessMode>
        <BT_BuildType>Debug</BT_BuildType>
        <DCC_Namespace>Winapi;System.Win;Data.Win;Datasnap.Win;Web.Win;Soap.Win;Xml.Win;Bde;$(DCC_Namespace)</DCC_Namespace>
        <DCC_UsePackage>vclwinx;DataSnapServer;FMXTMSFNCCorePkgDXE15;fmx;emshosting;vclie;DbxCommonDriver;bindengine;IndyIPCommon;VCLRESTComponents;DBXMSSQLDriver;FireDACCommonODBC;emsclient;FireDACCommonDriver;appanalytics;IndyProtocols;vclx;Skia.Package.RTL;IndyIPClient;dbxcds;vcledge;bindcompvclwinx;FmxTeeUI;emsedge;bindcompfmx;DBXFirebirdDriver;inetdb;FireDACSqliteDriver;DbxClientDriver;FireDACASADriver;Tee;soapmidas;vclactnband;TeeUI;fmxFireDAC;dbexpress;FireDACInfxDriver;DBXMySQLDriver;VclSmp;inet;DataSnapCommon;vcltouch;fmxase;DBXOdbcDriver;dbrtl;FireDACDBXDriver;Skia.Package.FMX;FireDACOracleDriver;fmxdae;TeeDB;FireDACMSAccDriver;CustomIPTransport;FireDACMSSQLDriver;DataSnapIndy10ServerTransport;FMXTMSFNCCloudPackPkgDXE15;DataSnapConnectors;vcldsnap;DBXInterBaseDriver;FireDACMongoDBDriver;IndySystem;FireDACTDataDriver;Skia.Package.VCL;vcldb;vclFireDAC;bindcomp;FireDACCommon;DataSnapServerMidas;FireDACODBCDriver;emsserverresource;IndyCore;RESTBackendComponents;bindcompdbx;rtl;FireDACMySQLDriver;FireDACADSDriver;RESTComponents;DBXSqliteDriver;vcl;IndyIPServer;dsnapxml;dsnapcon;DataSnapClient;DataSnapProviderClient;adortl;DBXSybaseASEDriver;TMSWEBCorePkgDXE15;DBXDb2Driver;vclimg;DataSnapFireDAC;emsclientfiredac;FireDACPgDriver;FireDAC;FireDACDSDriver;inetdbxpress;xmlrtl;tethering;bindcompvcl;dsnap;CloudService;DBXSybaseASADriver;DBXOracleDriver;FireDACDb2Driver;DBXInformixDriver;VCLTMSFNCCloudPackPkgDXE15;fmxobj;bindcompvclsmp;FMXTee;DataSnapNativeClient;TMSWEBCorePkgLibDXE15;VCLTMSFNCCorePkgDXE15;DatasnapConnectorsFreePascal;soaprtl;soapserver;FireDACIBDriver;$(DCC_UsePackage)</DCC_UsePackage>
        <Manifest_File>$(BDS)\bin\default_app.manifest</Manifest_File>
        <VerInfo_IncludeVerInfo>true</VerInfo_IncludeVerInfo>
        <VerInfo_Locale>1033</VerInfo_Locale>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_1)'!=''">
        <DCC_DebugDCUs>true</DCC_DebugDCUs>
        <DCC_DebugInfoInExe>true</DCC_DebugInfoInExe>
        <DCC_Define>DEBUG;$(DCC_Define)</DCC_Define>
        <DCC_GenerateStackFrames>true</DCC_GenerateStackFrames>
        <DCC_IntegerOverflowCheck>true</DCC_IntegerOverflowCheck>
        <DCC_Optimize>false</DCC_Optimize>
        <DCC_RangeChecking>true</DCC_RangeChecking>
        <DCC_RemoteDebug>true</DCC_RemoteDebug>
        <TMSWebDebugInfo>2</TMSWebDebugInfo>
        <TMSWebDefines>$(TMSWebDefines);DEBUG</TMSWebDefines>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_1_Win32)'!=''">
        <AppDPIAwarenessMode>PerMonitorV2</AppDPIAwarenessMode>
        <DCC_RemoteDebug>false</DCC_RemoteDebug>
        <TMSWebOutputPath>C:\EVIDENT_Deploy\$(config)</TMSWebOutputPath>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_2)'!=''">
        <DCC_DebugInformation>0</DCC_DebugInformation>
        <DCC_Define>RELEASE;$(DCC_Define)</DCC_Define>
        <DCC_LocalDebugSymbols>false</DCC_LocalDebugSymbols>
        <DCC_SymbolReferenceInfo>0</DCC_SymbolReferenceInfo>
        <TMSWebDefines>$(TMSWebDefines);RELEASE</TMSWebDefines>
        <TMSWebObfuscation>2</TMSWebObfuscation>
        <TMSWebOptimization>2</TMSWebOptimization>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_2_Win32)'!=''">
        <AppDPIAwarenessMode>PerMonitorV2</AppDPIAwarenessMode>
    </PropertyGroup>
    <ItemGroup>
        <DelphiCompile Include="$(MainSource)">
            <MainSource>MainSource</MainSource>
        </DelphiCompile>
        <DCCReference Include="..\libs\dx-library\DX.WebLib.XData.pas"/>
        <DCCReference Include="..\libs\dx-library\DX.WEBLib.Config.pas"/>
        <DCCReference Include="..\libs\dx-library\DX.WEBLib.Logger.pas"/>
        <DCCReference Include="..\libs\dx-library\DX.WEBLib.SysUtils.pas"/>
        <DCCReference Include="..\libs\dx-library\DX.WEBLib.Tabulator.pas"/>
        <DCCReference Include="Base.Form.pas">
            <Form>FormBase</Form>
            <DesignClass>TWebForm</DesignClass>
        </DCCReference>
        <DCCReference Include="Base.Form.Status.pas">
            <Form>FormBaseStatus</Form>
            <DesignClass>TWebForm</DesignClass>
        </DCCReference>
        <DCCReference Include="App.Controller.pas"/>
        <DCCReference Include="Main.DM.pas">
            <Form>DMMain</Form>
            <DesignClass>TWebDataModule</DesignClass>
        </DCCReference>
        <DCCReference Include="F00.Login.Form.pas">
            <Form>FormLogin</Form>
            <DesignClass>TWebForm</DesignClass>
        </DCCReference>
        <DCCReference Include="F01.Wartezimmer.Form.pas">
            <Form>FormWartezimmer</Form>
            <DesignClass>TWebForm</DesignClass>
        </DCCReference>
        <DCCReference Include="F02.Uebersicht.Form.pas">
            <Form>FormUebersicht</Form>
            <DesignClass>TWebForm</DesignClass>
        </DCCReference>
        <DCCReference Include="F04.Test.Behandlung.Befund.Form.pas">
            <Form>FormBehandlungBefundTest</Form>
            <DesignClass>TWebForm</DesignClass>
        </DCCReference>
        <DCCReference Include="Unit2.pas">
            <Form>Form1</Form>
            <DesignClass>TWebForm</DesignClass>
        </DCCReference>
        <DCCReference Include="Model\Stammdaten.Patient.pas"/>
        <DCCReference Include="F04.Behandlung.Befund.Form.pas">
            <Form>FormBehandlungBefund</Form>
            <FormType>dfm</FormType>
            <DesignClass>TWebForm</DesignClass>
        </DCCReference>
        <DCCReference Include="Zahnschema.Frame.pas">
            <Form>FrameZahnschema</Form>
            <FormType>dfm</FormType>
            <DesignClass>TWebFrame</DesignClass>
        </DCCReference>
        <None Include="index.html"/>
        <None Include="Manifest.json"/>
        <None Include="serviceworker.js"/>
        <None Include="WebConfig.json"/>
        <RcItem Include="favicon.ico">
            <ResourceType>ICON</ResourceType>
            <ResourceId>Icon_1</ResourceId>
        </RcItem>
        <None Include="assets\Thumbs.db"/>
        <None Include="assets\.DS_Store"/>
        <None Include="assets\css\01-wartezimmer.css"/>
        <None Include="assets\css\02-uebersicht.css"/>
        <None Include="assets\css\04-behandlung-befund-default.css"/>
        <None Include="assets\css\styleguide.css"/>
        <None Include="assets\css\00-sperrbildschirm.css"/>
        <None Include="assets\css\dialog.css"/>
        <None Include="assets\css\globals.css"/>
        <RcItem Include="assets\WorkspacesLogo_64x64.png">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>PngImage_1</ResourceId>
        </RcItem>
        <RcItem Include="assets\WorkspacesLogo_1024x1024.png">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>PngImage_2</ResourceId>
        </RcItem>
        <RcItem Include="assets\WorkspacesLogo_256x256.png">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>PngImage_3</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\badge-1.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_1</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-22.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_2</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\icon-calendar.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_3</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-23.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_4</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-21.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_5</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\front-22.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_6</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\<EMAIL>">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>PngImage_4</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-20.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_7</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-24.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_8</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-30.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_9</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-18.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_10</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\signet-1.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_11</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\icon-warning.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_12</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-19.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_13</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\icon-plus.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_14</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-31.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_15</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\icon-versicherung.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_16</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-25.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_17</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\fl-che.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_18</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\icon-work.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_19</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\ellipse-8.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_20</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\line--8.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_21</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-27.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_22</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\signet-2.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_23</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\icon-arbeitgeber.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_24</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\front-19.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_25</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\signet-3.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_26</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\<EMAIL>">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>PngImage_5</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-26.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_27</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-3.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_28</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\icon-mmo.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_29</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\icon-dentist.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_30</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-82.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_31</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-69.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_32</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\devider.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_33</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\icon-plus-1.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_34</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\signet.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_35</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-68.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_36</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\icon-close.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_37</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-83.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_38</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-2.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_39</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_40</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\ellipse-8-5.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_41</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\icon-search.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_42</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\<EMAIL>">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>PngImage_6</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-81.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_43</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-95.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_44</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\icon-dots.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_45</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\icon-versicherungstr-ger.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_46</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-94.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_47</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-80.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_48</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-1.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_49</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\ellipse-8-4.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_50</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\icon-arbeitgeber-1.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_51</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\icon-calendar-8.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_52</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-5.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_53</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-84.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_54</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-90.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_55</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\<EMAIL>">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>PngImage_7</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\icon.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_56</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\icon-voice.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_57</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-91.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_58</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-85.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_59</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-4.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_60</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\ellipse-8-1.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_61</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\icon-chevron-down.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_62</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-6.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_63</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-93.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_64</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-87.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_65</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-78.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_66</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\icon-next-event.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_67</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-79.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_68</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-86.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_69</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-92.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_70</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-7.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_71</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\icon-new-workspace.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_72</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\icon-filter.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_73</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-74.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_74</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-75.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_75</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\front.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_76</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\badge.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_77</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\icon-placeholder.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_78</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-9.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_79</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-88.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_80</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\fl-che-5.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_81</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\icon-versicherungstr-ger-1.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_82</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-77.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_83</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-76.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_84</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\front-6.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_85</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\fl-che-4.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_86</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-89.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_87</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-8.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_88</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\icon-next-event-1.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_89</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\icon-female.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_90</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\icon-calendar-1.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_91</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\icon-dots-1.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_92</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-66.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_93</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-72.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_94</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\line-.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_95</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\icon-chevron-down-3.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_96</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-73.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_97</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-67.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_98</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\front-3.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_99</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\fl-che-1.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_100</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\icon-calendar-2.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_101</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-71.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_102</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-65.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_103</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\icon-dentist-1.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_104</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-64.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_105</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-70.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_106</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\ellipse-5-1.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_107</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-17.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_108</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\<EMAIL>">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>PngImage_8</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\icon-play.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_109</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-16.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_110</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\ellipse-5.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_111</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\icon-notification.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_112</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\ellipse-7.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_113</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-28.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_114</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-14.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_115</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\ellipse-7-1.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_116</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\icon-chevron-right.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_117</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\front-16.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_118</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-15.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_119</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-29.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_120</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\line--6.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_121</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-11.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_122</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\ellipse-7-5.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_123</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\icon-female-1.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_124</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\<EMAIL>">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>PngImage_9</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\icon-open.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_125</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\ellipse-7-4.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_126</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-10.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_127</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\icon-maximize.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_128</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\k-rzel.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_129</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\ellipse-5-4.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_130</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\line--1.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_131</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-12.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_132</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\line--13.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_133</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\wurzel-13.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_134</ResourceId>
        </RcItem>
        <RcItem Include="assets\img\ellipse-5-5.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_135</ResourceId>
        </RcItem>
        <None Include="assets\Logo\WorkspacesLogo.afdesign"/>
        <RcItem Include="assets\Logo\WorkspacesLogo.svg">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>SvgImage_136</ResourceId>
        </RcItem>
        <RcItem Include="assets\WorkspacesLogo_512x512.png">
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>PngImage_10</ResourceId>
        </RcItem>
        <None Include="assets\fonts\Inter-Italic.woff2"/>
        <None Include="assets\fonts\Inter.woff2"/>
        <None Include="assets\css\main.css"/>
        <None Include="assets\css\normalize.css"/>
        <None Include="assets\css\normalize.min.css"/>
        <None Include="assets\fonts\InterVariable-Italic.woff2"/>
        <None Include="assets\fonts\InterVariable.woff2"/>
        <None Include="assets\css\font-inter.css"/>
        <BuildConfiguration Include="Base">
            <Key>Base</Key>
        </BuildConfiguration>
        <BuildConfiguration Include="Debug">
            <Key>Cfg_1</Key>
            <CfgParent>Base</CfgParent>
        </BuildConfiguration>
        <BuildConfiguration Include="Release">
            <Key>Cfg_2</Key>
            <CfgParent>Base</CfgParent>
        </BuildConfiguration>
    </ItemGroup>
    <ProjectExtensions>
        <Borland.Personality>Delphi.Personality.12</Borland.Personality>
        <Borland.ProjectType>Application</Borland.ProjectType>
        <BorlandProject>
            <Delphi.Personality>
                <Source>
                    <Source Name="MainSource">Workspaces.Web.dpr</Source>
                </Source>
                <Excluded_Packages/>
            </Delphi.Personality>
            <Deployment Version="5">
                <DeployFile LocalName="Manifest.json" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="WebConfig.json" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="Win32\Debug\Workspaces.Web.exe" Configuration="Debug" Class="ProjectOutput">
                    <Platform Name="Win32">
                        <RemoteName>Workspaces.Web.exe</RemoteName>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\.DS_Store" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\Logo\WorkspacesLogo.afdesign" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\Logo\WorkspacesLogo.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\Thumbs.db" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\WorkspacesLogo_1024x1024.png" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\WorkspacesLogo_256x256.png" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\WorkspacesLogo_512x512.png" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\WorkspacesLogo_64x64.png" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\css\00-sperrbildschirm.css" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\css\01-wartezimmer.css" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\css\02-uebersicht.css" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\css\04-behandlung-befund-default.css" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\css\dialog.css" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\css\font-inter.css" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\css\globals.css" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\css\main.css" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\css\normalize.css" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\css\normalize.min.css" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\css\styleguide.css" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\fonts\Inter-Italic.woff2" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\fonts\Inter.woff2" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\fonts\InterVariable-Italic.woff2" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\fonts\InterVariable.woff2" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\badge-1.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\badge.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\devider.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\ellipse-5-1.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\ellipse-5-4.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\ellipse-5-5.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\ellipse-5.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\ellipse-7-1.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\ellipse-7-4.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\ellipse-7-5.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\ellipse-7.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\ellipse-8-1.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\ellipse-8-4.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\ellipse-8-5.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\ellipse-8.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\fl-che-1.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\fl-che-4.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\fl-che-5.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\fl-che.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\front-16.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\front-19.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\front-22.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\front-3.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\front-6.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\front.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\icon-arbeitgeber-1.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\icon-arbeitgeber.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\icon-calendar-1.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\icon-calendar-2.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\icon-calendar-8.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\icon-calendar.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\icon-chevron-down-3.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\<EMAIL>" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\icon-chevron-down.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\icon-chevron-right.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\icon-close.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\icon-dentist-1.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\icon-dentist.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\icon-dots-1.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\icon-dots.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\icon-female-1.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\icon-female.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\icon-filter.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\icon-maximize.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\icon-mmo.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\icon-new-workspace.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\icon-next-event-1.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\icon-next-event.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\icon-notification.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\icon-open.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\<EMAIL>" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\icon-placeholder.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\icon-play.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\icon-plus-1.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\icon-plus.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\icon-search.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\icon-versicherung.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\icon-versicherungstr-ger-1.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\icon-versicherungstr-ger.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\icon-voice.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\icon-warning.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\icon-work.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\icon.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\k-rzel.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\line--1.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\line--13.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\line--6.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\line--8.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\line-.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\<EMAIL>" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\<EMAIL>" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\<EMAIL>" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\<EMAIL>" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\signet-1.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\signet-2.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\signet-3.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\signet.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-1.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-10.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-11.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-12.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-13.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-14.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-15.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-16.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-17.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-18.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-19.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-2.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-20.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-21.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-22.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-23.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-24.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-25.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-26.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-27.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-28.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-29.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-3.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-30.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-31.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-4.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-5.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-6.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-64.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-65.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-66.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-67.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-68.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-69.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-7.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-70.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-71.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-72.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-73.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-74.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-75.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-76.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-77.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-78.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-79.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-8.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-80.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-81.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-82.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-83.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-84.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-85.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-86.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-87.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-88.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-89.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-9.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-90.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-91.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-92.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-93.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-94.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel-95.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="assets\img\wurzel.svg" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="favicon.ico" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="index.html" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="serviceworker.js" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployClass Name="AdditionalDebugSymbols">
                    <Platform Name="Win32">
                        <Operation>0</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="DebugSymbols">
                    <Platform Name="Win32">
                        <Operation>0</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="DependencyFramework">
                    <Platform Name="Win32">
                        <Operation>0</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="DependencyModule">
                    <Platform Name="Win32">
                        <Operation>0</Operation>
                        <Extensions>.dll;.bpl</Extensions>
                    </Platform>
                </DeployClass>
                <DeployClass Required="true" Name="DependencyPackage">
                    <Platform Name="Win32">
                        <Operation>0</Operation>
                        <Extensions>.bpl</Extensions>
                    </Platform>
                </DeployClass>
                <DeployClass Name="File">
                    <Platform Name="Win32">
                        <Operation>0</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Required="true" Name="ProjectOutput">
                    <Platform Name="Win32">
                        <Operation>0</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="ProjectUWPManifest">
                    <Platform Name="Win32">
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="UWP_DelphiLogo150">
                    <Platform Name="Win32">
                        <RemoteDir>Assets</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="UWP_DelphiLogo44">
                    <Platform Name="Win32">
                        <RemoteDir>Assets</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <ProjectRoot Platform="Win32" Name="$(PROJECTNAME)"/>
            </Deployment>
            <Platforms>
                <Platform value="Win32">True</Platform>
                <Platform value="Win64">False</Platform>
            </Platforms>
        </BorlandProject>
        <ProjectFileVersion>12</ProjectFileVersion>
    </ProjectExtensions>
    <Import Project="$(BDS)\Bin\CodeGear.Delphi.Targets" Condition="Exists('$(BDS)\Bin\CodeGear.Delphi.Targets')"/>
    <Import Project="$(APPDATA)\Embarcadero\$(BDSAPPDATABASEDIR)\$(PRODUCTVERSION)\UserTools.proj" Condition="Exists('$(APPDATA)\Embarcadero\$(BDSAPPDATABASEDIR)\$(PRODUCTVERSION)\UserTools.proj')"/>
    <Import Project="$(MSBuildProjectName).deployproj" Condition="Exists('$(MSBuildProjectName).deployproj')"/>
</Project>
