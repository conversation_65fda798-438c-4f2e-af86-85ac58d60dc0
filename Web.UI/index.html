﻿<!DOCTYPE html>
<html lang="de">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
  <meta content="width=1920, maximum-scale=1.0" name="viewport"/> <!-- Wir skalieren das feste 1080p/1920 Design -->

  <link href="assets/css/normalize.min.css" rel="stylesheet"/>

  <link href="assets/css/styleguide.css" rel="stylesheet" type="text/css"/>  <!-- Farben und Fonts -->
  <link href="assets/css/globals.css" rel="stylesheet" type="text/css"/>     <!-- globale Settings aus Figma -->
  <link href="assets/css/main.css" rel="stylesheet"/>                        <!-- Container Klassen für die Form Inhalte -->
  <link href="assets/css/dialog.css" rel="stylesheet" type="text/css"/>

  <noscript>Your browser does not support JavaScript!</noscript>

  <!-- TODO Variablen sollen in 2.9 auch hier funktionieren
  <link href="$(MidResFileName)" rel="icon" sizes="192x192" />
  <link href="$(MidResFileName)" rel="apple-touch-icon" />
  -->
  <link href="WorkspacesLogo_256x256.png" rel="icon" sizes="192x192"/>
  <link href="WorkspacesLogo_256x256.png" rel="apple-touch-icon"/>

  <script src="serviceworker.js" type="text/javascript"></script>
  <meta $(Manifest)/>
  <link $(FavIcon)/>
  <title>$(ProjectName)</title>
  <script src="$(ProjectName).js" type="text/javascript"></script>

<body>

</body>
<script type="text/javascript">ProjectNameVersion = "$(ProjectName)"; <!--Captures project name AND version-->
  rtl.run();</script>

</html>


