﻿project
begin
  projectid = {93D7D940-F20A-46E1-910F-6D1C66BE898B}
  target
  begin
    name = Default
    targetid = {EE8C6DE9-1804-4385-A587-4E30AF98BDF9}
    rootaction
    begin
      action.script.run
      begin
        description = "ChatGPT HTML Post-Processor"
        id = {6CB2E241-917C-48BF-B55A-E1C0C7AD94F8}
        action.script
        begin
          name = OnExecute
          script >
          beginscript
            // Create a shell object to execute commands
            var WshShell = new ActiveXObject("WScript.Shell");
            
            // Define the path to chatgpt.exe using the FinalBuilder project directory, escaping backslashes
            var chatGptExe = FBPROJECTDIR + "\\chatgpt.exe";
            
            // Log the path of chatgpt.exe for debugging purposes
            Action.SendLogMessage(chatGptExe);
            
            // Execute the DOS command (chatgpt.exe --help) and capture its output
            var Exec = WshShell.Exec(chatGptExe + " --help");
            
            // Wait until the command completes and read the output
            var Output = "";
            while (!Exec.StdOut.AtEndOfStream) {
                Output += Exec.StdOut.ReadLine() + "\n"; // Read output line by line and add a newline
            }
            
            // Store the captured output in a FinalBuilder variable
            Action.SetVariable("ChatGPT_Output", Output);
            
            // Log a confirmation message indicating the output has been stored
            Action.SendLogMessage("Output has been stored in ChatGPT_Output:");
            
            // Log the actual output stored in the variable
            Action.SendLogMessage(ChatGPT_Output);
          endscript
          scriptlanguage = JavaScript
        end
      end
    end
  end
  variable
  begin
    name = ChatGPT_Output
    variablescope = vtProject
    variabletype = btString
  end
end