﻿unit Main.DM;

interface

uses
  System.SysUtils, System.Classes,
  JS, Web,
  Data.DB,
  WEBLib.Modules,

  XData.Web.Connection, XData.Web.Client,
  XData.Web.JsonDataset, XData.Web.Dataset,

  DX.WEBLib.Config, DX.WEBLib.Logger,

  Stammdaten.Patient;

type
  TDMMain = class(TWebDataModule)
    XDataConnection: TXDataWebConnection;
    QPatienten: TXDataWebDataSet;
    QPatientenPatnr: TIntegerField;
    QPatientenKasseId: TIntegerField;
    QPatientenKassekz: TStringField;
    QPatientenArbgeb: TStringField;
    QPatientenTelarbgeb: TStringField;
    QPatientenVersadr: TIntegerField;
    QPatientenAbwadr: TIntegerField;
    QPatientenHausarztadr: TIntegerField;
    QPatientenUeberweiseradr: TIntegerField;
    QPatientenNachrichtl1adr: TIntegerField;
    QPatientenNachrichtl2adr: TIntegerField;
    QPatientenMahnen: TIntegerField;
    QPatientenBankeinzug: TIntegerField;
    QPatientenProthzuschuss: TIntegerField;
    QPatientenAnamnese: TIntegerField;
    QPatientenEinldat1: TDateTimeField;
    QPatientenEinldat2: TDateTimeField;
    QPatientenKfozuschuss: TIntegerField;
    QPatientenFreiwVers: TIntegerField;
    QPatientenVersnr: TStringField;
    QPatientenKennz: TIntegerField;
    QPatientenGlobPatnr: TIntegerField;
    QPatientenGebdat: TDateTimeField;
    QPatientenGeschlecht: TStringField;
    QPatientenTitel: TStringField;
    QPatientenAnrede: TStringField;
    QPatientenBriefanrede: TStringField;
    QPatientenNamenszusatz: TStringField;
    QPatientenNachname: TStringField;
    QPatientenVorname: TStringField;
    QPatientenSoundexnachname: TStringField;
    QPatientenSoundexvorname: TStringField;
    QPatientenStrasse: TStringField;
    QPatientenLkz: TStringField;
    QPatientenPlz: TStringField;
    QPatientenOrt: TStringField;
    QPatientenPostfach: TStringField;
    QPatientenPsfPlz: TStringField;
    QPatientenPsfOrt: TStringField;
    QPatientenTelefon1: TStringField;
    QPatientenTelefon2: TStringField;
    QPatientenFax: TStringField;
    QPatientenEmail: TStringField;
    QPatientenBeruf: TStringField;
    QPatientenDuzfreund: TStringField;
    QPatientenAnzahlung: TFloatField;
    QPatientenStammbehandler: TStringField;
    QPatientenAufnahmedatum: TDateTimeField;
    QPatientenRechbankkonto: TIntegerField;
    QPatientenArchiviert: TStringField;
    QPatientenArchivgesperrt: TStringField;
    QPatientenUebernahme: TStringField;
    QPatientenArchiviertam: TDateTimeField;
    QPatientenArchiviertbenutzer: TStringField;
    QPatientenKontaktart: TIntegerField;
    QPatientenVerstorben: TStringField;
    QPatientenSms1: TStringField;
    QPatientenGebort: TStringField;
    QPatientenGebname: TStringField;
    QPatientenVddsrzDefault: TStringField;
    QPatientenPrenotification: TIntegerField;
    QPatientenSperreTis: TStringField;
    QPatientenSperreEmail: TStringField;
    QPatientenSperreSms: TStringField;
    QPatientenSperrePatinfo: TStringField;
    QPatientenHatBeihilfe: TStringField;
    QPatientenZuzahlungsbefreit: TStringField;
    QPatientenZuzahlungsbefreitDatum: TDateTimeField;
    QPatientenNamenszusatzEgk: TStringField;
    QPatientenVorsatzwort: TStringField;
    QPatientenStrasseEgk: TStringField;
    QPatientenHausnummer: TStringField;
    QPatientenAnschriftenzusatz: TStringField;
    QPatientenLkzEgk: TStringField;
    QPatientenPlzEgk: TStringField;
    QPatientenOrtEgk: TStringField;
    QPatientenPsfLkz: TStringField;
    QPatientenInfektionsrisiko: TStringField;
    QPatientenTisRemind: TStringField;
    QPatientenTisRemindSms: TStringField;
    QPatientenTisRemindEmail: TStringField;
    QPatientenTisRemindSmsVers: TStringField;
    QPatientenTisRemindEmailVers: TStringField;
    QPatientenTisRemindSmsRech: TStringField;
    QPatientenTisRemindEmailRech: TStringField;
    QPatientenTisRemindTyp: TIntegerField;
    QPatientenProthzussystem: TIntegerField;
    QPatientenKeinrecall: TStringField;
    QPatientenHomecommunityid: TStringField;
    QPatientenEparechtebis: TDateTimeField;
    QPatientenEparechtLeDocs: TStringField;
    QPatientenEparechtKtrDocs: TStringField;
    QPatientenEparechtVersDocs: TStringField;
    QPatientenEtiId: TLargeintField;
    QPatientenEtiCreated: TDateTimeField;
    QPatientenEtiModified: TDateTimeField;
    QPatientenListView: TStringField;
    QEmailsForPatient: TXDataWebDataSet;
    QEmailsForPatientPatient: TXDataWebEntityField;
    QEmailsForPatientId: TLargeintField;
    QEmailsForPatientMessageid: TBlobField;
    QEmailsForPatientTyp: TIntegerField;
    QEmailsForPatientGesendet: TDateTimeField;
    QEmailsForPatientBetreff: TStringField;
    QEmailsForPatientTitel: TStringField;
    QEmailsForPatientNotiz: TBlobField;
    QEmailsForPatientWichtigkeit: TIntegerField;
    QEmailsForPatientRichtung: TIntegerField;
    XDataWebClient: TXDataWebClient;
    procedure WebDataModuleCreate(Sender: TObject);
    procedure XDataConnectionConnect(Sender: TObject);
    procedure XDataConnectionError(Error: TXDataWebConnectionError);
    procedure XDataWebClientRequest(Request: TXDataClientRequest);
  private
    FToken: string;

    FWartezimmerPatienten: TPatientInfoList;

    FAktuellerPatientInfo: TPatientInfo;
  public
    property AktuellerPatientInfo: TPatientInfo read FAktuellerPatientInfo write FAktuellerPatientInfo;
    property WartezimmerPatienten: TPatientInfoList read FWartezimmerPatienten write FWartezimmerPatienten;

    [async]
    procedure Login(const AUser: string; const APassword: string);
    [async]
    procedure RefreshWartezimmer(ANeuePatientenProc: TProc = nil);

    [async]
    procedure GetFirstPatientFromWartezimmer(ASuccessProc: TProc<TPatientInfo>);

  end;

var
  DMMain: TDMMain;

implementation

{%CLASSGROUP 'Vcl.Controls.TControl'}
{$R *.dfm}

procedure TDMMain.WebDataModuleCreate(Sender: TObject);
var
  LUrl: string;
begin
  DXLog('DMMain initializing ...');
  // Das DMMain lädt auch die Config
  TWebConfig.Load(
    procedure
    begin
      LUrl := TWebConfig.Value('Application', 'RestApiUrl');
      XDataConnection.URL := LUrl;
      XDataConnection.Connected := true;
    end);

end;

{ TDMMain }

procedure TDMMain.GetFirstPatientFromWartezimmer(ASuccessProc: TProc<TPatientInfo>);
var
  LResponse: TXDataClientResponse;
  LPatienten: TPatientInfoList;
  LPatientInfo: TPatientInfo;
begin
  LResponse := await(XDataWebClient.RawInvokeAsync('IWartezimmerService.GetWartezimmer', []));
  if LResponse.StatusCode = 200 then
  begin
    LPatienten := TPatientInfoList.Create;
    LPatienten.LoadFromJSArray(TJSArray(TJSObject(LResponse.Result)['value']));

    if LPatienten.Items.Count > 0 then
    begin
      LPatientInfo := LPatienten.Items[0];
    end
    else
    begin
      LPatientInfo := nil;
    end;
    ASuccessProc(LPatientInfo);
  end
  else
  begin
    raise Exception.Create('Wartezimer kann nicht abgefragt werden!');
  end;
end;

procedure TDMMain.Login(const AUser: string; const APassword: string);
var
  LResponse: TXDataClientResponse;

begin
  LResponse := await(XDataWebClient.RawInvokeAsync('ILoginService.Login', [AUser, APassword]));
  if LResponse.StatusCode = 200 then
  begin
    FToken := string(TJSObject(LResponse.Result)['value']);
    DXLog('User [%s] logged in!', [AUser]);
  end
  else
  begin
    raise Exception.Create('Login ungültig');
  end;

end;

procedure TDMMain.RefreshWartezimmer(ANeuePatientenProc: TProc = nil);
var
  LResponse: TXDataClientResponse;
  LPatientenImWartezimmer: integer;
  LAktuellePatienten, LNeuePatienten: TPatientInfoList;
  LPatientInfo: TPatientInfo;
  LListenUnterschiedlich: Boolean;
  i: integer;
  LPatientGefunden: Boolean;
begin
  LResponse := await(XDataWebClient.RawInvokeAsync('IWartezimmerService.GetWartezimmer', []));
  if LResponse.StatusCode = 200 then
  begin
    LNeuePatienten := TPatientInfoList.Create;
    LNeuePatienten.LoadFromJSArray(TJSArray(TJSObject(LResponse.Result)['value']));
    LPatientenImWartezimmer := LNeuePatienten.Items.Count;

    // Prüfen, ob sich die Liste geändert hat
    LListenUnterschiedlich := False;

    // Unterschiedliche Länge bedeutet Änderung
    LAktuellePatienten := FWartezimmerPatienten;
    if (LAktuellePatienten = nil) or (LAktuellePatienten.Items.Count <> LNeuePatienten.Items.Count) then
      LListenUnterschiedlich := true
    else
    begin
      // Gleiche Länge - Inhalte vergleichen
      for i := 0 to LAktuellePatienten.Items.Count - 1 do
      begin
        if LAktuellePatienten.Items[i].Patient.PatNr <> LNeuePatienten.Items[i].Patient.PatNr then
        begin
          LListenUnterschiedlich := true;
          Break;
        end;
      end;
    end;

    // Neue Liste übernehmen
    FWartezimmerPatienten := LNeuePatienten;

    DXLog('Patienten wurden geladen: ' + IntToStr(LPatientenImWartezimmer));

    if LPatientenImWartezimmer = 0 then
    begin
      FAktuellerPatientInfo := nil;
    end
    else
    begin
      // Prüfen, ob aktueller Patient noch in der Liste ist
      DXLog('Prüfen, ob aktueller Patient noch da ist');
      LPatientGefunden := False;
      if Assigned(FAktuellerPatientInfo) then
      begin
        for LPatientInfo in FWartezimmerPatienten.Items do
        begin
          if LPatientInfo.Patient.PatNr = FAktuellerPatientInfo.Patient.PatNr then
          begin
            LPatientGefunden := true;
            Break;
          end;
        end;
      end;

      // Nur neu setzen, wenn der bisherige Patient nicht gefunden wurde
      if not LPatientGefunden then
      begin
        FAktuellerPatientInfo := FWartezimmerPatienten.Items.First;
      end;
    end;

    // Callback nur ausführen, wenn es Änderungen gibt und Callback definiert ist
    if Assigned(ANeuePatientenProc) and LListenUnterschiedlich then
    begin
      DXLog('Im Wartezimmer sind neue Patienten ...');
      ANeuePatientenProc;
    end;

  end
  else
  begin
    raise Exception.Create('Fehler beim Abruf von IWartezimmerService.GetWartezimmer');
  end;
end;

procedure TDMMain.XDataConnectionConnect(Sender: TObject);
begin
  //Todo: einen Callback einbauen, der solange die UI blockiert, bis die Connection steht.
  DXLog('REST API connected: ' + XDataConnection.URL);

{$IFDEF DEBUG}
  //Autologin in debug mode
  Login('test', 'test');
{$ENDIF}
end;

procedure TDMMain.XDataConnectionError(Error: TXDataWebConnectionError);
begin
  DXLog('REST API failed to connect: ' + XDataConnection.URL + ' ' + Error.ErrorMessage);
end;

procedure TDMMain.XDataWebClientRequest(Request: TXDataClientRequest);
begin
  Request.Request.Headers.SetValue('Authorization', 'Bearer ' + FToken);
end;

end.

