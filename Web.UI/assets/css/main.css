﻿/*** Various styles ***/

/* Farben und Fonts sind StyleGuide.css */

/* Container-basiertes Layout */
html,
body {
  margin: 0;
  padding: 0;

  /* <PERSON><PERSON> Browser-Scrollbars */
  overflow: hidden;

  width: 100vw;
  height: 100vh;
}

.app-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;

  /* Höhe des Status Panels wird im OnCreate des Status Forms berechnet*/
  height: 100vh;

  /* Scrollen innerhalb des Containers */
  overflow: auto;

  box-sizing: border-box;
  padding-right: 20px;

  /* wichtig: kein Flex hier */
  display: block;

  /* Basis Farbe und Fonts */
  background-color: var(--concrete);
  font-family: var(--font-family);
  font-size: var(--font-size-m);
}

.screen-container {
  /* Platz für die vertikale Scrollbar */
  width: 100% !important;
}

/* Zahnschema Placeholder */
.zahnschema-placeholder {
  width: 100%;
  height: 100%;

  /* hellgrau */
  background-color: #ddd;

  /* Flexbox aktivieren */
  display: flex;

  /* vertikal zentrieren */
  align-items: center;

  /* horizontal zentrieren */
  justify-content: center;

  font-size: 24px;
  color: #555;
  box-sizing: border-box;
}