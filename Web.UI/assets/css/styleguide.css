/* New font system */
/* --font-family is defined in font-inter.css */

@import url("./font-inter.css");

:root {
  --alto: #d9d9d9;
  --black: #000000;
  --concrete: #f2f2f2;
  --gray: #808080;
  --lily-white: #e5f1ff;
  --magenta: #ed6ea7;
  --mercury: #e6e6e6;
  --mercury-2: #e3e6e8;
  --midnight-blue: #002d67;
  --midnight-blue-2: #003966;
  --oslo-gray: #828c90;
  --white: #ffffff;

  --font-size-xxs: 0.625rem;
  --font-size-xs: 0.83125rem;
  --font-size-s: 0.875rem;
  --font-size-m: 1rem;
  --font-size-l: 1.5rem;
  --font-size-xl: 2rem;
  --font-size-xxl: 2.5rem;
}

/* Typography System */
/* Base text style */
.text-regular {
  font-family: var(--font-family);
  font-size: var(--font-size-m);
  font-style: normal;
  font-weight: 400;
  letter-spacing: 0;
}

/* New semantic typography classes */
.text-medium-primary-base {
  color: var(--midnight-blue);
  font-family: var(--font-family);
  font-size: var(--font-size-m);
  font-style: normal;
  font-weight: 500;
}

.text-light-primary-base {
  color: var(--midnight-blue);
  font-family: var(--font-family);
  font-size: var(--font-size-m);
  font-style: normal;
  font-weight: 300;
}

.text-medium-primary-xs {
  color: var(--midnight-blue);
  font-family: var(--font-family);
  font-size: var(--font-size-xxs);
  font-style: normal;
  font-weight: 500;
}

.text-light-secondary-md {
  color: var(--oslo-gray);
  font-family: var(--font-family);
  font-size: var(--font-size-s);
  font-style: normal;
  font-weight: 300;
}

.text-semibold-primary-base {
  color: var(--midnight-blue);
  font-family: var(--font-family);
  font-size: var(--font-size-m);
  font-style: normal;
  font-weight: 600;
}

.text-regular-primary-md {
  color: var(--midnight-blue);
  font-family: var(--font-family);
  font-size: var(--font-size-s);
  font-style: normal;
  font-weight: 400;
}

.text-bold-primary-base {
  color: var(--midnight-blue);
  font-family: var(--font-family);
  font-size: var(--font-size-m);
  font-style: normal;
  font-weight: 700;
}

.text-semibold-primary-md {
  color: var(--midnight-blue);
  font-family: var(--font-family);
  font-size: var(--font-size-s);
  font-style: normal;
  font-weight: 600;
}

.text-regular-primary-base {
  color: var(--midnight-blue);
  font-family: var(--font-family);
  font-size: var(--font-size-m);
  font-style: normal;
  font-weight: 400;
}

.text-condensed-primary-base {
  color: var(--midnight-blue);
  font-family: var(--font-family);
  font-size: var(--font-size-m);
  font-style: normal;
  font-weight: 400;
  /* Condensed style - can be simulated with letter-spacing */
  letter-spacing: -0.02em;
}

.text-light-tertiary-md {
  color: var(--gray);
  font-family: var(--font-family);
  font-size: var(--font-size-s);
  font-style: normal;
  font-weight: 300;
}

.text-light-primary-md {
  color: var(--midnight-blue);
  font-family: var(--font-family);
  font-size: var(--font-size-s);
  font-style: normal;
  font-weight: 300;
}

.text-medium-inverse-sm {
  color: var(--white);
  font-family: var(--font-family);
  font-size: var(--font-size-xs);
  font-style: normal;
  font-weight: 500;
}

.text-medium-inverse-base {
  color: var(--white);
  font-family: var(--font-family);
  font-size: var(--font-size-m);
  font-style: normal;
  font-weight: 500;
}

.text-medium-inverse-xs {
  color: var(--white);
  font-family: var(--font-family);
  font-size: var(--font-size-xxs);
  font-style: normal;
  font-weight: 500;
}

/*********************************************/
/* Legacy classes for backward compatibility */
/* Should be removed later                   */
/*********************************************/

.inter-medium-midnight-blue-16px {
  color: var(--midnight-blue);
  font-family: var(--font-family);
  font-size: var(--font-size-m);
  font-style: normal;
  font-weight: 500;
}

.frutigerltstd-45-light-midnight-blue-16px {
  color: var(--midnight-blue);
  font-family: var(--font-family);
  font-size: var(--font-size-m);
  font-style: normal;
  font-weight: 300;
}

.inter-medium-midnight-blue-10px {
  color: var(--midnight-blue);
  font-family: var(--font-family);
  font-size: var(--font-size-xxs);
  font-style: normal;
  font-weight: 500;
}

.frutigerltstd-45-light-oslo-gray-14px {
  color: var(--oslo-gray);
  font-family: var(--font-family);
  font-size: var(--font-size-s);
  font-style: normal;
  font-weight: 300;
}

.inter-semi-bold-midnight-blue-16px {
  color: var(--midnight-blue);
  font-family: var(--font-family);
  font-size: var(--font-size-m);
  font-style: normal;
  font-weight: 600;
}

.inter-normal-midnight-blue-14px {
  color: var(--midnight-blue);
  font-family: var(--font-family);
  font-size: var(--font-size-s);
  font-style: normal;
  font-weight: 400;
}

.inter-bold-midnight-blue-16px {
  color: var(--midnight-blue);
  font-family: var(--font-family);
  font-size: var(--font-size-m);
  font-style: normal;
  font-weight: 700;
}

.inter-semi-bold-midnight-blue-14px {
  color: var(--midnight-blue);
  font-family: var(--font-family);
  font-size: var(--font-size-s);
  font-style: normal;
  font-weight: 600;
}

.inter-normal-midnight-blue-16px {
  color: var(--midnight-blue);
  font-family: var(--font-family);
  font-size: var(--font-size-m);
  font-style: normal;
  font-weight: 400;
}

.frutigerltstd-57condensed-normal-midnight-blue-16px {
  color: var(--midnight-blue);
  font-family: var(--font-family);
  font-size: var(--font-size-m);
  font-style: normal;
  font-weight: 400;
  letter-spacing: -0.02em; /* Simulate condensed style */
}

.frutigerltstd-45-light-gray-14px {
  color: var(--gray);
  font-family: var(--font-family);
  font-size: var(--font-size-s);
  font-style: normal;
  font-weight: 300;
}

.frutigerltstd-45-light-midnight-blue-14px {
  color: var(--midnight-blue);
  font-family: var(--font-family);
  font-size: var(--font-size-s);
  font-style: normal;
  font-weight: 300;
}

.inter-medium-white-13-3px {
  color: var(--white);
  font-family: var(--font-family);
  font-size: var(--font-size-xs);
  font-style: normal;
  font-weight: 500;
}

.inter-medium-white-16px {
  color: var(--white);
  font-family: var(--font-family);
  font-size: var(--font-size-m);
  font-style: normal;
  font-weight: 500;
}

.inter-medium-white-10px {
  color: var(--white);
  font-family: var(--font-family);
  font-size: var(--font-size-xxs);
  font-style: normal;
  font-weight: 500;
}
