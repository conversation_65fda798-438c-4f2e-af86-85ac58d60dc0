html,
body {
  background-color: var(--concrete);
  font-family: var(--font-family);
  font-size: var(--font-size-m);
}

* {
  box-sizing: border-box;
}

.screen a {
  display: contents;
  text-decoration: none;
}

.valign-text-middle {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

input:focus {
  outline: none;
}

.toolbar {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.83vw;
  position: relative;
}

.signet {
  height: 1.67vw;
  position: relative;
  width: 1.92vw;
}

.actiongroup {
  align-items: flex-start;
  display: inline-flex;
  flex: 0 0 auto;
  height: 2.5vw;
  position: relative;
}

.icon {
  height: 1.25vw;
  position: relative;
  width: 1.25vw;
}

.user {
  align-items: center;
  border-radius: 0.31vw;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.21vw;
  height: 2.5vw;
  padding: 0.0vw 0.62vw;
  position: relative;
}

.tb {
  letter-spacing: 0;
  line-height: 0.83vw;
  margin-top: -0.02vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.navigation {
  align-items: center;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  gap: 1.25vw;
  position: relative;
  width: 100%;
}

.navigation-1 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.83vw;
  position: relative;
}

.page-content {
  align-items: flex-start;
  align-self: stretch;
  border-radius: 0.31vw;
  display: flex;
  flex: 1;
  flex-grow: 1;
  gap: 0.62vw;
  position: relative;
  width: 100%;
}

.tab-bar {
  align-self: stretch;
  display: flex;
  gap: 0.1vw;
  width: 100%;
}

.label-7 {
  letter-spacing: 0;
  line-height: 1.0vw;
  margin-top: -0.03vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.label-9 {
  letter-spacing: 0;
  line-height: 1.0vw;
  margin-top: -0.03vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.label-10 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  height: 1.04vw;
  position: relative;
}

.label-11 {
  letter-spacing: 0;
  line-height: 1.0vw;
  margin-top: -0.03vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.label-12 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  height: 1.04vw;
  position: relative;
}

.label-13 {
  letter-spacing: 0;
  line-height: 1.0vw;
  margin-top: -0.03vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.label-14 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  height: 1.04vw;
  position: relative;
}

.label-15 {
  letter-spacing: 0;
  line-height: 1.0vw;
  margin-top: -0.03vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.label-17 {
  letter-spacing: 0;
  line-height: 1.0vw;
  margin-top: -0.03vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.tags {
  align-items: center;
  background-color: var(--white);
  border: 0.05vw solid;
  border-color: var(--mercury);
  border-radius: 0.21vw;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.21vw;
  height: 1.67vw;
  justify-content: center;
  min-width: 1.67vw;
  padding: 0.0vw 0.21vw;
  position: relative;
}

.tagline {
  color: var(--midnight-blue);
  font-weight: 400;
  line-height: 1.25vw;
  position: relative;
  text-align: center;
  white-space: nowrap;
  width: fit-content;
}

.action-group {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.42vw;
  height: 1.25vw;
  justify-content: center;
  padding: 0.21vw 0.0vw;
  position: relative;
}

.button-1 {
  align-items: center;
  display: flex;
  gap: 0.99vw;
  height: 1.25vw;
  justify-content: center;
  margin-bottom: -0.21vw;
  margin-top: -0.21vw;
  padding: 0.52vw 0.83vw;
  position: relative;
  width: 1.25vw;
}

.image {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  height: 2.92vw;
  position: relative;
  width: 3.75vw;
}

.rectangle-922 {
  height: 2.92vw;
  object-fit: cover;
  position: relative;
  width: 3.75vw;
}

.paragraph-3 {
  flex: 1;
  letter-spacing: 0;
  line-height: 1.09vw;
  margin-top: -0.05vw;
  position: relative;
}

.input {
  background-color: transparent;
  border: 0;
  color: var(--midnight-blue);
  flex: 1;
  flex-grow: 1;
  font-weight: 400;
  line-height: 1.25vw;
  margin-top: -0.05vw;
  padding: 0;
  position: relative;
}

.input::placeholder {
  color: #002d6799;
}

.bersicht-3 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  height: 1.67vw;
  justify-content: center;
  padding: 0.52vw 0.0vw;
  position: relative;
}

.tab {
  align-items: center;
  flex: 0 0 auto;
  position: relative;
}

.heading-6 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  height: 1.04vw;
  position: relative;
}

.stammdaten {
  align-items: flex-start;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 0.21vw;
  position: relative;
  width: 100%;
}

.list-item-title {
  letter-spacing: 0;
  line-height: 1.09vw;
  margin-top: -0.05vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.frame-3 {
  align-items: center;
  display: flex;
  left: 0;
  padding: 0.0vw 20.0vw 0.0vw 0.0vw;
  position: absolute;
  top: 0;
  width: 6.88vw;
}

.frame-4 {
  align-items: center;
  display: flex;
  left: 0;
  padding: 0.0vw 20.0vw 0.0vw 0.0vw;
  position: absolute;
  top: 1.25vw;
  width: 6.88vw;
}

.two-line-list-treating-doctor {
  align-items: flex-start;
  align-self: stretch;
  border-radius: 0.31vw;
  display: flex;
  flex: 0 0 auto;
  gap: 14.53vw;
  justify-content: space-around;
  min-width: 10.1vw;
  padding: 0.42vw;
  position: relative;
  width: 100%;
}

.anamnese {
  align-items: flex-start;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 0.83vw;
  position: relative;
  width: 100%;
}

.icon-calendar {
  height: 1.25vw;
  margin-bottom: -0.62vw;
  margin-top: -0.62vw;
  position: relative;
  width: 1.25vw;
}

.anamnese-item {
  align-items: flex-start;
  display: inline-flex;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 0.42vw;
  position: relative;
}

.tags-container {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.42vw;
  position: relative;
}

.tags-container-1 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.42vw;
  position: relative;
}

.text-container-1 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.52vw;
  justify-content: center;
  position: relative;
}