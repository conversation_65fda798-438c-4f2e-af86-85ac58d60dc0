/* screen - 04-behandlung-befund-default */

.x04-behandlung-befund-default {
  align-items: flex-start;
  background-color: var(--concrete);
  display: flex;
  flex-direction: column;
  max-width: 100.0vw;
  min-height: 100vh;
  position: relative;
}

.x04-behandlung-befund-default .container {
  align-items: flex-start;
  display: flex;
  flex: 1;
  flex-direction: column;
  flex-grow: 1;
  gap: 0.83vw;
  max-width: 100.0vw;
  padding: 1.25vw;
  position: relative;
  width: 100%;
}

.x04-behandlung-befund-default .header-1 {
  align-items: flex-start;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 0.83vw;
  position: relative;
  width: 100%;
}

.x04-behandlung-befund-default .header {
  align-items: center;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  justify-content: space-between;
  position: relative;
  width: 100%;
}

.x04-behandlung-befund-default .button {
  align-items: center;
  display: flex;
  gap: 0.99vw;
  height: 2.5vw;
  justify-content: center;
  position: relative;
  width: 2.5vw;
}

.x04-behandlung-befund-default .badge {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.42vw;
  position: relative;
}

.x04-behandlung-befund-default .frame-683 {
  align-items: center;
  background-color: var(--magenta);
  border-radius: 0.83vw;
  flex-direction: column;
  gap: 0.52vw;
  height: 1.67vw;
  justify-content: center;
  padding: 0.42vw 0.31vw;
  width: 1.67vw;
}

.x04-behandlung-befund-default .body {
  align-items: flex-start;
  align-self: stretch;
  display: flex;
  flex: 1;
  flex-direction: column;
  flex-grow: 1;
  gap: 0.42vw;
  position: relative;
  width: 100%;
}

.x04-behandlung-befund-default .bersicht {
  align-items: center;
  cursor: pointer;
  display: inline-flex;
  flex: 0 0 auto;
  height: 1.67vw;
  justify-content: center;
  padding: 0.52vw 0.0vw;
  position: relative;
}

.x04-behandlung-befund-default .label {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  height: 1.04vw;
  margin-bottom: -0.21vw;
  margin-top: -0.21vw;
  position: relative;
}

.x04-behandlung-befund-default .label-1 {
  letter-spacing: 0;
  line-height: 1.0vw;
  margin-top: -0.03vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x04-behandlung-befund-default .bersicht-1 {
  border-bottom-style: solid;
  border-bottom-width: 0.1vw;
  border-color: var(--magenta);
}

.x04-behandlung-befund-default .label-2 {
  letter-spacing: 0;
  line-height: 1.0vw;
  margin-top: -0.03vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x04-behandlung-befund-default .label-3 {
  letter-spacing: 0;
  line-height: 1.0vw;
  margin-top: -0.03vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x04-behandlung-befund-default .left {
  align-items: flex-start;
  align-self: stretch;
  display: flex;
  flex: 1;
  flex-direction: column;
  flex-grow: 1;
  gap: 0.62vw;
  position: relative;
}

.x04-behandlung-befund-default .content-box {
  align-items: flex-start;
  align-self: stretch;
  background-color: var(--white);
  border-radius: 0.42vw;
  display: flex;
  flex: 1;
  flex-direction: column;
  flex-grow: 1;
  gap: 0.83vw;
  padding: 0.83vw;
  position: relative;
  width: 100%;
}

.x04-behandlung-befund-default .zahnschema {
  align-items: center;
  align-self: stretch;
  display: flex;
  flex: 1;
  flex-direction: column;
  flex-grow: 1;
  justify-content: center;
  position: relative;
  width: 100%;
}

.x04-behandlung-befund-default .zahnschema-1 {
  align-items: center;
  background-color: var(--white);
  display: inline-flex;
  flex-direction: column;
  height: 18.12vw;
  justify-content: center;
  position: relative;
}

.x04-behandlung-befund-default .oberkiefer {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  position: relative;
}

.x04-behandlung-befund-default .quadrant {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.05vw;
  position: relative;
}

.x04-behandlung-befund-default .zahn {
  height: 8.65vw;
  position: relative;
  width: 2.81vw;
}

.x04-behandlung-befund-default .info {
  align-items: center;
  display: flex;
  gap: 0.1vw;
  height: 0.83vw;
  justify-content: center;
  left: 0.05vw;
  position: absolute;
  top: 0.21vw;
  width: 2.71vw;
}

.x04-behandlung-befund-default .icon-3 {
  height: 0.83vw;
  position: relative;
  width: 0.83vw;
}

.x04-behandlung-befund-default .zahn-1 {
  align-items: flex-start;
  height: 6.46vw;
  min-width: 2.08vw;
  padding: 0.03vw 0;
  position: absolute;
  top: 1.25vw;
}

.x04-behandlung-befund-default .overlap-group {
  height: 4.17vw;
  position: relative;
  width: 2.08vw;
}

.x04-behandlung-befund-default .front {
  height: 1.98vw;
  left: 0;
  position: absolute;
  top: 2.19vw;
  width: 2.08vw;
}

.x04-behandlung-befund-default .wurzel {
  height: 2.24vw;
  left: 0.16vw;
  position: absolute;
  top: 0;
  width: 1.77vw;
}

.x04-behandlung-befund-default .zahn-2 {
  align-items: flex-start;
  height: 6.46vw;
  min-width: 2.08vw;
  padding: 0.03vw 0;
  position: relative;
  top: 1.25vw;
}

.x04-behandlung-befund-default .divider {
  align-items: center;
  display: flex;
  gap: 0.52vw;
  height: 8.12vw;
  justify-content: center;
  position: relative;
  width: 1.04vw;
}

.x04-behandlung-befund-default .line {
  align-self: stretch;
  object-fit: cover;
  position: relative;
  width: 0.05vw;
}

.x04-behandlung-befund-default .quadrant-2 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.05vw;
  height: 8.65vw;
  position: relative;
}

.x04-behandlung-befund-default .icon-4 {
  height: 0.83vw;
  position: relative;
  width: 0.83vw;
}

.x04-behandlung-befund-default .zahn-5 {
  align-items: flex-start;
  flex-direction: column;
  gap: 0.21vw;
  min-height: 6.46vw;
  position: absolute;
  top: 1.25vw;
  width: 2.08vw;
}

.x04-behandlung-befund-default .flche {
  height: 2.08vw;
  width: 2.08vw;
}

.x04-behandlung-befund-default .krzel {
  align-items: center;
  display: flex;
  gap: 0.1vw;
  height: 0.83vw;
  justify-content: center;
  left: 0;
  position: absolute;
  top: 7.71vw;
  width: 2.81vw;
}

.x04-behandlung-befund-default .krzel-1 {
  height: 0.83vw;
  position: relative;
  width: 0.83vw;
}

.x04-behandlung-befund-default .icon-5 {
  height: 0.83vw;
  position: relative;
  width: 0.83vw;
}

.x04-behandlung-befund-default .ellipse-container {
  border: 0.03vw solid;
  border-color: var(--midnight-blue-2);
  border-radius: 0.31vw;
  height: 0.62vw;
  left: 0.1vw;
  position: relative;
  top: 0.1vw;
  width: 0.62vw;
}

.x04-behandlung-befund-default .ellipse-8 {
  height: 0.26vw;
  left: 0.31vw;
  position: absolute;
  top: 0.05vw;
  width: 0.31vw;
}

.x04-behandlung-befund-default .ellipse-5 {
  height: 0.31vw;
  left: 0.16vw;
  position: absolute;
  top: 0.31vw;
  width: 0.26vw;
}

.x04-behandlung-befund-default .ellipse-7 {
  height: 0.26vw;
  left: 0;
  position: absolute;
  top: 0.05vw;
  width: 0.31vw;
}

.x04-behandlung-befund-default .ellipse-3 {
  background-color: var(--midnight-blue-2);
  border: 0.03vw solid;
  border-color: var(--white);
  border-radius: 0.1vw;
  height: 0.21vw;
  left: 0.21vw;
  position: absolute;
  top: 0.21vw;
  width: 0.21vw;
}

.x04-behandlung-befund-default .zahnnummern {
  align-items: center;
  align-self: stretch;
  background-color: var(--mercury-2);
  display: flex;
  flex: 0 0 auto;
  gap: 1.04vw;
  position: relative;
  width: 100%;
}

.x04-behandlung-befund-default .links {
  align-items: center;
  border-radius: 0.21vw 0.0vw 0.0vw 0.21vw;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.05vw;
  position: relative;
}

.x04-behandlung-befund-default .zahnnummer {
  align-items: center;
  background-color: var(--concrete);
  display: flex;
  gap: 0.52vw;
  height: 0.83vw;
  justify-content: center;
  padding: 0.0vw 1.09vw;
  position: relative;
  width: 2.81vw;
}

.x04-behandlung-befund-default .nummer {
  height: 0.83vw;
  letter-spacing: 0;
  line-height: 1.13vw;
  margin-left: -0.1vw;
  margin-right: -0.1vw;
  margin-top: -0.05vw;
  position: relative;
  text-align: center;
  white-space: nowrap;
  width: 0.83vw;
}

.x04-behandlung-befund-default .rechts {
  align-items: center;
  display: flex;
  gap: 0.05vw;
  position: relative;
  width: 22.5vw;
}

.x04-behandlung-befund-default .zahnnummer-rechts-7 {
  background-color: var(--mercury-2);
}

.x04-behandlung-befund-default .zahnnummer-rechts-8 {
  background-color: var(--concrete);
  margin-right: -0.36vw;
}

.x04-behandlung-befund-default .unterkiefer {
  align-items: flex-end;
  display: inline-flex;
  flex: 0 0 auto;
  justify-content: center;
  position: relative;
}

.x04-behandlung-befund-default .info-1 {
  align-items: center;
  display: flex;
  gap: 0.1vw;
  height: 0.83vw;
  justify-content: center;
  left: 0.05vw;
  position: absolute;
  top: 7.6vw;
  width: 2.71vw;
}

.x04-behandlung-befund-default .icon-6 {
  height: 0.83vw;
  position: relative;
  width: 0.83vw;
}

.x04-behandlung-befund-default .zahn-3 {
  align-items: flex-end;
  height: 6.46vw;
  min-width: 2.08vw;
  padding: 0.05vw 0;
  position: absolute;
  top: 0.94vw;
}

.x04-behandlung-befund-default .overlap-group-1 {
  height: 4.11vw;
  position: relative;
  width: 2.08vw;
}

.x04-behandlung-befund-default .wurzel-1 {
  height: 2.24vw;
  left: 0.16vw;
  position: absolute;
  top: 1.88vw;
  width: 1.77vw;
}

.x04-behandlung-befund-default .front-1 {
  height: 1.98vw;
  left: 0;
  position: absolute;
  top: 0;
  width: 2.08vw;
}

.x04-behandlung-befund-default .zahn-4 {
  align-items: flex-end;
  height: 6.46vw;
  min-width: 2.08vw;
  padding: 0.05vw 0;
  position: relative;
  top: 0.94vw;
}

.x04-behandlung-befund-default .zahn-6 {
  align-items: flex-start;
  flex-direction: column;
  gap: 0.21vw;
  min-height: 6.46vw;
  position: absolute;
  top: 0.94vw;
  width: 2.08vw;
}

.x04-behandlung-befund-default .krzel-2 {
  align-items: center;
  display: flex;
  gap: 0.1vw;
  height: 0.83vw;
  justify-content: center;
  left: 0;
  position: absolute;
  top: 0.1vw;
  width: 2.81vw;
}

.x04-behandlung-befund-default .krzel-3 {
  height: 0.83vw;
  position: relative;
  width: 0.83vw;
}

.x04-behandlung-befund-default .divider-1 {
  align-items: center;
  display: flex;
  gap: 0.52vw;
  height: 8.54vw;
  justify-content: center;
  position: relative;
  width: 1.04vw;
}

.x04-behandlung-befund-default .icon-7 {
  height: 0.83vw;
  position: relative;
  width: 0.83vw;
}

.x04-behandlung-befund-default .icon-8 {
  height: 0.83vw;
  position: relative;
  width: 0.83vw;
}

.x04-behandlung-befund-default .content-box-1 {
  width: 100%;
}

.x04-behandlung-befund-default .tab-items-1 {
  background-color: var(--white);
  border-radius: 0.42vw 0.0vw 0.0vw 0.0vw;
  display: inline-flex;
  flex: 0 0 auto;
}

.x04-behandlung-befund-default .label-4 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  height: 1.04vw;
  position: relative;
}

.x04-behandlung-befund-default .label-5 {
  letter-spacing: 0;
  line-height: 1.0vw;
  margin-top: -0.03vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x04-behandlung-befund-default .tab-items {
  background-color: var(--mercury);
  display: inline-flex;
  gap: 0.21vw;
  height: 2.08vw;
  padding: 0.0vw 0.83vw;
}

.x04-behandlung-befund-default .text-container {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.52vw;
  justify-content: center;
  position: relative;
}

.x04-behandlung-befund-default .label-6 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  height: 1.04vw;
  position: relative;
}

.x04-behandlung-befund-default .label-8 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  height: 1.04vw;
  position: relative;
}

.x04-behandlung-befund-default .tab-items-2 {
  background-color: var(--mercury);
  border-radius: 0.0vw 0.42vw 0.0vw 0.0vw;
  display: flex;
  flex: 1;
  flex-grow: 1;
}

.x04-behandlung-befund-default .label-16 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  height: 1.04vw;
  position: relative;
}

.x04-behandlung-befund-default .container-1 {
  align-items: flex-start;
  align-self: stretch;
  background-color: var(--white);
  border-radius: 0.0vw 0.0vw 0.31vw 0.31vw;
  display: flex;
  flex: 1;
  flex-direction: column;
  flex-grow: 1;
  padding: 0.83vw;
  width: 100%;
}

.x04-behandlung-befund-default .krzel-4 {
  align-items: center;
  align-self: stretch;
  display: flex;
  flex: 1;
  flex-direction: column;
  flex-grow: 1;
  justify-content: center;
  position: relative;
  width: 100%;
}

.x04-behandlung-befund-default .leistungs-historie::-webkit-scrollbar {
  display: none;
  width: 0;
}

.x04-behandlung-befund-default .leistungs-historie {
  align-items: flex-start;
  align-self: stretch;
  background-color: var(--white);
  display: flex;
  flex: 1;
  flex-direction: column;
  flex-grow: 1;
  gap: 0.83vw;
  overflow-y: scroll;
  position: relative;
  width: 100%;
}

.x04-behandlung-befund-default .kassen-leistungen-2 {
  align-items: flex-start;
  display: inline-flex;
  flex-direction: column;
  gap: 0.42vw;
}

.x04-behandlung-befund-default .heading {
  align-items: center;
  display: inline-flex;
  height: 1.04vw;
  position: relative;
}

.x04-behandlung-befund-default .heading-1 {
  letter-spacing: 0.05vw;
  line-height: 0.88vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x04-behandlung-befund-default .kassen-leistungen {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.62vw;
  position: relative;
}

.x04-behandlung-befund-default .kassen-leistungen-1 {
  align-items: flex-start;
  display: inline-flex;
  flex-direction: column;
  gap: 0.21vw;
}

.x04-behandlung-befund-default .kassen-leistungen-3 {
  align-items: center;
  align-self: stretch;
  display: flex;
  gap: 0.42vw;
  width: 100%;
}

.x04-behandlung-befund-default .tagline-3 {
  letter-spacing: 0;
  text-align: center;
}

.x04-behandlung-befund-default .kassen-leistungen-4 {
  align-items: center;
  align-self: stretch;
  display: flex;
  gap: 0.42vw;
  width: 100%;
}

.x04-behandlung-befund-default .tags-2 {
  background-color: var(--white);
  border: 0.05vw solid;
  border-color: var(--midnight-blue);
  border-radius: 0.21vw;
  min-width: 1.67vw;
  padding: 0.0vw 0.21vw;
}

.x04-behandlung-befund-default .tagline-1 {
  color: var(--alto);
  font-weight: 400;
  text-align: center;
}

.x04-behandlung-befund-default .right {
  align-items: center;
  align-self: stretch;
  display: flex;
  flex: 1;
  flex-grow: 1;
  gap: 0.52vw;
  position: relative;
}

.x04-behandlung-befund-default .content-box-2 {
  background-color: var(--white);
  gap: 0.83vw;
  padding: 0.83vw;
}

.x04-behandlung-befund-default .heading-4 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  height: 1.04vw;
  position: relative;
}

.x04-behandlung-befund-default .heading-5 {
  letter-spacing: 0;
  line-height: 1.0vw;
  margin-top: -0.03vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x04-behandlung-befund-default .container-2 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  justify-content: flex-end;
}

.x04-behandlung-befund-default .icon-1 {
  height: 1.25vw;
  margin-bottom: -0.52vw;
  margin-left: -0.83vw;
  margin-right: -0.83vw;
  margin-top: -0.52vw;
  position: relative;
  width: 1.25vw;
}

.x04-behandlung-befund-default .verlauf::-webkit-scrollbar {
  display: none;
  width: 0;
}

.x04-behandlung-befund-default .verlauf {
  align-items: flex-start;
  align-self: stretch;
  background: linear-gradient(0deg, rgba(239, 242, 245, 0.5) 0%, rgba(239, 242, 245, 0.5) 100%), linear-gradient(0deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 1) 100%);
  display: flex;
  flex: 1;
  flex-direction: column;
  flex-grow: 1;
  gap: 0.62vw;
  overflow: hidden;
  overflow-y: scroll;
  padding: 0.0vw 0.42vw;
  position: relative;
  width: 100%;
}

.x04-behandlung-befund-default .leistung {
  align-items: center;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  gap: 0.83vw;
  position: relative;
  width: 100%;
}

.x04-behandlung-befund-default .stepper {
  align-items: center;
  align-self: stretch;
  display: flex;
  flex-direction: column;
  gap: 0.21vw;
  padding: 0.21vw 0.0vw 0.0vw;
  position: relative;
  width: 0.83vw;
}

.x04-behandlung-befund-default .marker {
  align-items: center;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  gap: 0.52vw;
  position: relative;
  width: 100%;
}

.x04-behandlung-befund-default .ellipse {
  border: 0.1vw solid;
  border-color: var(--midnight-blue-2);
  border-radius: 0.42vw;
  flex: 1;
  flex-grow: 1;
  height: 0.83vw;
  position: relative;
}

.x04-behandlung-befund-default .rectangle {
  background-color: var(--mercury);
  flex: 1;
  flex-grow: 1;
  position: relative;
  width: 0.1vw;
}

.x04-behandlung-befund-default .erfasste-leistungen {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  gap: 0.42vw;
  padding: 0.0vw 0.0vw 1.25vw;
  position: relative;
  width: 44.27vw;
}

.x04-behandlung-befund-default .button-2 {
  align-items: center;
  background-color: var(--mercury-2);
  border-radius: 0.21vw;
  display: inline-flex;
  gap: 0.42vw;
  height: 1.25vw;
  justify-content: center;
  padding: 0.62vw 0.42vw;
  position: relative;
}

.x04-behandlung-befund-default .button-3 {
  letter-spacing: 0;
  line-height: 1.09vw;
  margin-bottom: -0.49vw;
  margin-top: -0.6vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x04-behandlung-befund-default .accordion-items-1 {
  align-items: center;
  border: 0.05vw solid;
  border-color: var(--mercury-2);
  border-radius: 0.31vw;
  gap: 0.42vw;
  height: 2.92vw;
  padding: 0.0vw 0.83vw;
}

.x04-behandlung-befund-default .content {
  align-items: center;
  display: flex;
  flex: 1;
  flex-grow: 1;
  justify-content: space-between;
  position: relative;
}

.x04-behandlung-befund-default .text-goes-here {
  color: var(--midnight-blue);
  font-weight: 400;
  line-height: 1.25vw;
  margin-top: -0.05vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x04-behandlung-befund-default .action-group-1 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.42vw;
  position: relative;
}

.x04-behandlung-befund-default .button-4 {
  align-items: center;
  align-self: stretch;
  border-radius: 0.21vw;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.42vw;
  justify-content: center;
  padding: 0.62vw 0.42vw;
  position: relative;
}

.x04-behandlung-befund-default .button-5 {
  letter-spacing: 0;
  line-height: 1.0vw;
  margin-top: -0.05vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x04-behandlung-befund-default .button-6 {
  align-items: center;
  align-self: stretch;
  border-radius: 0.21vw;
  display: flex;
  gap: 0.99vw;
  justify-content: center;
  padding: 0.0vw 0.83vw;
  position: relative;
  width: 1.25vw;
}

.x04-behandlung-befund-default .icon-chevron-down {
  height: 1.25vw;
  margin-left: -0.83vw;
  margin-right: -0.83vw;
  position: relative;
  width: 1.25vw;
}

.x04-behandlung-befund-default .card-image {
  align-items: center;
  background-color: var(--white);
  border: 0.05vw solid;
  border-color: var(--mercury);
  border-radius: 0.42vw;
  display: flex;
  flex: 0 0 auto;
  gap: 0.62vw;
  padding: 0.62vw;
  position: relative;
  width: 10.83vw;
}

.x04-behandlung-befund-default .frame-1287 {
  align-items: center;
  flex: 1;
  flex-grow: 1;
  gap: 0.83vw;
  justify-content: center;
}

.x04-behandlung-befund-default .frame-1286 {
  align-items: flex-start;
  flex: 1;
  flex-direction: column;
  flex-grow: 1;
}

.x04-behandlung-befund-default .heading-2 {
  align-items: center;
  align-self: stretch;
  display: flex;
  height: 1.04vw;
  position: relative;
  width: 100%;
}

.x04-behandlung-befund-default .heading-3 {
  flex: 1;
  letter-spacing: 0;
  line-height: 1.0vw;
  margin-top: -0.03vw;
  position: relative;
}

.x04-behandlung-befund-default .frame-1289 {
  align-items: flex-start;
  align-self: stretch;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 0.52vw;
  width: 100%;
}

.x04-behandlung-befund-default .paragraph-2 {
  align-items: center;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  position: relative;
  width: 100%;
}

.x04-behandlung-befund-default .accordion-items-2 {
  align-items: flex-start;
  border-radius: 0.31vw;
  flex: 0 0 auto;
  flex-direction: column;
}

.x04-behandlung-befund-default .accordion-items-3 {
  align-items: center;
  border: 0.05vw solid;
  border-color: var(--mercury-2);
  border-radius: 0.31vw 0.31vw 0.0vw 0.0vw;
  gap: 0.42vw;
  height: 2.92vw;
  padding: 0.0vw 0.83vw;
}

.x04-behandlung-befund-default .button-8 {
  align-items: center;
  align-self: stretch;
  border-radius: 0.21vw;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.21vw;
  justify-content: center;
  padding: 0.62vw 0.21vw;
  position: relative;
}

.x04-behandlung-befund-default .frame-2 {
  align-items: flex-start;
  align-self: stretch;
  background-color: var(--white);
  border-bottom-style: solid;
  border-bottom-width: 0.05vw;
  border-color: var(--mercury);
  border-left-style: solid;
  border-left-width: 0.05vw;
  border-radius: 0.0vw 0.0vw 0.31vw 0.31vw;
  border-right-style: solid;
  border-right-width: 0.05vw;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 0.52vw;
  padding: 0.52vw;
  width: 100%;
}

.x04-behandlung-befund-default .wurzelbehandlung-l-vit-e-expanded {
  align-items: flex-start;
  align-self: stretch;
  background-color: var(--white);
  display: flex;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 0.52vw;
  position: relative;
  width: 100%;
}

.x04-behandlung-befund-default .frame-1316 {
  align-items: flex-start;
  align-self: stretch;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 0.83vw;
  width: 100%;
}

.x04-behandlung-befund-default .leistungen {
  align-items: flex-start;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 0.42vw;
  position: relative;
  width: 100%;
}

.x04-behandlung-befund-default .tags-1 {
  background-color: var(--lily-white);
  border-radius: 0.31vw;
  padding: 0.21vw;
}

.x04-behandlung-befund-default .tagline-2 {
  letter-spacing: -0.02vw;
  margin-top: -0.05vw;
}

.x04-behandlung-befund-default .paragraph {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  justify-content: center;
  position: relative;
}

.x04-behandlung-befund-default .paragraph-1 {
  letter-spacing: 0;
  line-height: 1.09vw;
  margin-top: -0.05vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x04-behandlung-befund-default .operativer-eingriff {
  color: var(--midnight-blue);
  font-family: var(--font-family);
  font-size: var(--font-size-s);
  font-weight: 700;
  letter-spacing: 0;
  line-height: 1.09vw;
  margin-top: -0.05vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x04-behandlung-befund-default .apicale-aufhellung {
  color: var(--midnight-blue);
  font-family: var(--font-family);
  font-size: var(--font-size-s);
  font-weight: 700;
  letter-spacing: 0;
  line-height: 1.09vw;
  margin-top: -0.05vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x04-behandlung-befund-default .messaufnahme {
  color: var(--midnight-blue);
  font-family: var(--font-family);
  font-size: var(--font-size-s);
  font-weight: 700;
  letter-spacing: 0;
  line-height: 1.09vw;
  margin-top: -0.05vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x04-behandlung-befund-default .frame-1320 {
  align-items: flex-start;
  align-self: stretch;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 0.21vw;
  width: 100%;
}

.x04-behandlung-befund-default .frame-1319 {
  align-items: center;
  align-self: stretch;
  flex: 0 0 auto;
  gap: 0.42vw;
  width: 100%;
}

.x04-behandlung-befund-default .paragraph-4 {
  color: var(--midnight-blue);
  font-family: var(--font-family);
  font-size: var(--font-size-s);
  font-style: italic;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 1.09vw;
  margin-top: -0.05vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x04-behandlung-befund-default .accordion-items {
  align-items: center;
  align-self: stretch;
  background-color: var(--white);
  border: 0.05vw solid;
  border-color: var(--mercury-2);
  border-radius: 0.31vw;
  display: flex;
  gap: 0.42vw;
  height: 3.33vw;
  padding: 0.0vw 0.83vw;
  position: relative;
  width: 100%;
}

.x04-behandlung-befund-default .card-image-1 {
  align-items: center;
  background-color: var(--white);
  border: 0.05vw solid;
  border-color: var(--mercury);
  border-radius: 0.42vw;
  display: flex;
  flex: 0 0 auto;
  gap: 0.62vw;
  padding: 0.62vw;
  position: relative;
  width: 12.66vw;
}

.x04-behandlung-befund-default .rectangle-922-1 {
  height: 1.82vw;
  object-fit: cover;
  position: relative;
  width: 3.75vw;
}

.x04-behandlung-befund-default .frame-1286-1 {
  align-items: flex-start;
  display: flex;
  flex: 1;
  flex-direction: column;
  flex-grow: 1;
  position: relative;
}

.x04-behandlung-befund-default .icon-chevron-down-1 {
  height: 1.25vw;
  margin-left: -97.08vw;
  margin-top: -471.67vw;
  position: relative;
  width: 1.25vw;
}

.x04-behandlung-befund-default .area-footer {
  align-items: center;
  align-self: stretch;
  border-radius: 0.0vw 0.0vw 0.83vw 0.83vw;
  display: flex;
  gap: 0.21vw;
  height: 2.08vw;
  position: relative;
  width: 100%;
}

.x04-behandlung-befund-default .button-7 {
  align-items: center;
  display: flex;
  gap: 0.99vw;
  height: 2.08vw;
  justify-content: center;
  padding: 0.52vw 0.83vw;
  position: relative;
  width: 2.08vw;
}

.x04-behandlung-befund-default .icon-2 {
  height: 1.25vw;
  margin-bottom: -0.1vw;
  margin-left: -0.42vw;
  margin-right: -0.42vw;
  margin-top: -0.1vw;
  position: relative;
  width: 1.25vw;
}

.x04-behandlung-befund-default .input-field {
  align-items: flex-start;
  display: flex;
  flex: 1;
  flex-direction: column;
  flex-grow: 1;
  position: relative;
}

.x04-behandlung-befund-default .container-3 {
  align-items: center;
  align-self: stretch;
  background-color: var(--white);
  border: 0.05vw solid;
  border-color: var(--mercury);
  border-radius: 0.31vw;
  display: flex;
  height: 2.08vw;
  padding: 0.42vw;
  width: 100%;
}

.x04-behandlung-befund-default .text-regular {
  align-items: center;
  display: flex;
  flex: 1;
  flex-grow: 1;
  position: relative;
}

.x04-behandlung-befund-default .paragraph-5 {
  align-items: center;
  display: flex;
  flex: 1;
  flex-grow: 1;
  justify-content: center;
  position: relative;
}

.x04-behandlung-befund-default .accordion-items-4 {
  align-self: stretch;
  background-color: var(--white);
  display: flex;
  position: relative;
  width: 100%;
}

.x04-behandlung-befund-default .action-group-1-4 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.42vw;
  position: relative;
}

.x04-behandlung-befund-default .container-4 {
  gap: 0.83vw;
  position: relative;
}

.x04-behandlung-befund-default .content-box-3 {
  align-items: flex-start;
  align-self: stretch;
  border-radius: 0.42vw;
  display: flex;
  flex: 1;
  flex-direction: column;
  flex-grow: 1;
  position: relative;
}

.x04-behandlung-befund-default .frame {
  display: flex;
  position: relative;
}

.x04-behandlung-befund-default .kassen-leistungen-5 {
  flex: 0 0 auto;
  position: relative;
}

.x04-behandlung-befund-default .leistung-4 {
  align-items: center;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  gap: 0.42vw;
  position: relative;
  width: 100%;
}

.x04-behandlung-befund-default .tab-items-3 {
  align-items: center;
  gap: 0.21vw;
  height: 2.08vw;
  padding: 0.0vw 0.83vw;
  position: relative;
}

.x04-behandlung-befund-default .tagline-4 {
  line-height: 1.25vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x04-behandlung-befund-default .tags-3 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.21vw;
  height: 1.67vw;
  justify-content: center;
  position: relative;
}

.x04-behandlung-befund-default .text-container-5 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.83vw;
  position: relative;
}

.x04-behandlung-befund-default .zahn-7 {
  display: flex;
  left: 0.36vw;
}

.x04-behandlung-befund-default .zahnnummer-rechts {
  align-items: center;
  display: flex;
  gap: 0.52vw;
  height: 0.83vw;
  justify-content: center;
  padding: 0.0vw 1.09vw;
  position: relative;
  width: 2.81vw;
}
