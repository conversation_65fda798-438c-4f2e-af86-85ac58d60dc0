.custom-dialog {
    background-color: var(--global-color-chromatic-100); /* <PERSON><PERSON><PERSON>ntergrund */
    border: 1px solid var(--semantic-color-stroke-default); /* Subtile Umrandung */
    border-radius: var(--global-radius-3x); /* Abgerundete Ecken */
    box-shadow: 0 4px 12px rgbaOCO(0, 0, 0, 0.1); /* Leichter Schatten für Tiefe */
    padding: 1.25vw; /* Innenabstand konsistent mit deinem Design */
    width: 400px; /* Feste Breite für Konsistenz */
    max-width: 90vw; /* Responsive Anpassung */
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%); /* Zentrierung */
    font-family: "Inter", Helvetica; /* Schriftart aus deinem Design */
  }
  
  /* Styling für den Titel (bereits vorhandene Klasse heading-2) */
  .custom-dialog .heading-2 {
    color: var(--semantic-color-text-heading-default); /* Dunkelblau */
    font-size: 2.083vw; /* Größe aus deinem CSS */
    line-height: 2.5vw;
    margin-bottom: 0.833vw; /* Abstand zum Inhalt */
  }
  
  /* Styling für den Inhalt (bereits vorhandene Klasse paragraph-2) */
  .custom-dialog .paragraph-2 {
    color: var(--semantic-color-text-paragraph-default); /* Dunkelblau */
    font-size: 0.833vw; /* Größe aus deinem CSS */
    line-height: 1.25vw;
    margin-bottom: 1.25vw; /* Abstand zum Button */
  }
  
  /* Styling für den Button (bereits vorhandene Klasse button) */
  .custom-dialog .button {
    background-color: var(--global-color-blue-20); /* Dunkelblau */
    color: var(--semantic-color-text-label-negative); /* Weiß */
    border-radius: var(--global-radius-3x);
    padding: 0 0.625vw;
    height: 2.083vw;
    font-size: 0.833vw;
    font-weight: 500;
    line-height: 1vw;
    transition: background-color 0.2s ease; /* Sanfte Hover-Animation */
  }
  
  .custom-dialog .button:hover {
    background-color: rgba(0, 45, 103, 0.9); /* Etwas helleres Blau bei Hover */
  }