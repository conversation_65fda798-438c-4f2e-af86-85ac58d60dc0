unit F01.Wartezimmer.Form;

interface

uses
  System.SysUtils, System.Classes,
  web,
  Vcl.Controls,
  WEBLib.Graphics, WEBLib.Controls, WEBLib.Forms, WEBLib.Dialogs,
  WEBLib.WebCtrls, Vcl.StdCtrls, WEBLib.StdCtrls, WEBLib.ExtCtrls,

  Base.Form.Status,
  Main.DM, Stammdaten.Patient;

type
  TFormWartezimmer = class(TFormBaseStatus)
    LabelPatientName: TWebLabel;
    DivStammdaten: TWebHTMLDiv;
    LabelVersicherungstraeger: TWebLabel;
    LabelVersicherung: TWebLabel;
    LabelBeruf: TWebLabel;
    LabelAufnahme: TWebLabel;
    LabelLetzterBesuch: TWebLabel;
    LabelArbeitgeber: TWebLabel;
    DivPatient1: TWebHTMLDiv;
    DivPatient2: TWebHTMLDiv;
    DivPatient3: TWebHTMLDiv;
    DivPatient4: TWebHTMLDiv;
    DivPatient5: TWebHTMLDiv;
    DivPatient6: TWebHTMLDiv;
    LabelListePatient1Name: TWebLabel;
    LabelListePatient1Info: TWebLabel;
    LabelListePatient2Info: TWebLabel;
    LabelListePatient2Name: TWebLabel;
    LabelListePatient3Info: TWebLabel;
    LabelListePatient3Name: TWebLabel;
    LabelListePatient4Info: TWebLabel;
    LabelListePatient4Name: TWebLabel;
    LabelListePatient5Info: TWebLabel;
    LabelListePatient5Name: TWebLabel;
    LabelListePatient6Info: TWebLabel;
    LabelListePatient6Name: TWebLabel;
    ButtonBehandlungStarten: TWebHTMLDiv;
    TimerWartezimmer: TWebTimer;
    LabelBehandler: TWebLabel;
    DivFaelligAm: TWebHTMLDiv;
    procedure ButtonBehandlungstartenClick(Sender: TObject);
    procedure buttonLogoutClick(Sender: TObject);
    procedure DivPatientDblClick(Sender: TObject);
    procedure DivPatientClick(Sender: TObject);
    procedure TimerWartezimmerTimer(Sender: TObject);
    procedure WebFormShow(Sender: TObject);
  private
    FPatientInfo: TPatientInfo;

  protected
    [async]
    procedure RefreshWartezimmer;
    procedure SetPatient(APatientInfo: TPatientInfo);
  public
    { Public declarations }
  end;

implementation

uses
  App.Controller, DX.WEBLib.Logger;

{$R *.dfm}

procedure TFormWartezimmer.ButtonBehandlungstartenClick(Sender: TObject);
begin
  inherited;
//Todo: Für Präsentation abgeschaltet
exit;
  TimerWartezimmer.Enabled := false;
  AppController.ShowFormUebersicht(FPatientInfo);
  DXLog('Übersicht ...');
  Close;
end;

procedure TFormWartezimmer.buttonLogoutClick(Sender: TObject);
begin
  AppController.Logout;
end;

procedure TFormWartezimmer.DivPatientDblClick(Sender: TObject);
begin
  inherited;
  ButtonBehandlungstartenClick(Sender);
end;

procedure TFormWartezimmer.DivPatientClick(Sender: TObject);
var
  LClassSelected: string;
  LCLassUnselected: string;
  LSelectedPatientIndex: integer;
begin
  inherited;
  LClassSelected := 'two-line-list-2 two-line-list-3';
  LCLassUnselected := 'two-line-list';
  LSelectedPatientIndex := -1;

  //Todo: Aktuell max 6 Patienten im Wartezimmer
  DivPatient1.ElementClassName := LCLassUnselected;
  DivPatient2.ElementClassName := LCLassUnselected;
  DivPatient3.ElementClassName := LCLassUnselected;
  DivPatient4.ElementClassName := LCLassUnselected;
  DivPatient5.ElementClassName := LCLassUnselected;
  DivPatient6.ElementClassName := LCLassUnselected;

  //Alle variablen Felder auf "null"
  LabelPatientName.Caption := '';
  LabelVersicherung.Caption := '';
  LabelVersicherungstraeger.Caption := '';
  LabelBeruf.Caption := '';
  LabelAufnahme.Caption := '';
  LabelLetzterBesuch.Caption := '';
  LabelArbeitgeber.Caption := '';
  LabelBehandler.Caption := '';

  DivFaelligAm.Visible := false; //Todo: Aktuell keine Fälligkeit

  if Sender = DivPatient1 then
  begin
    DivPatient1.ElementClassName := LClassSelected;
    LSelectedPatientIndex := 0;
  end
  else if Sender = DivPatient2 then
  begin
    DivPatient2.ElementClassName := LClassSelected;
    LSelectedPatientIndex := 1;
  end
  else if Sender = DivPatient3 then
  begin
    DivPatient3.ElementClassName := LClassSelected;
    LSelectedPatientIndex := 2;
  end
  else if Sender = DivPatient4 then
  begin
    DivPatient4.ElementClassName := LClassSelected;
    LSelectedPatientIndex := 3;
  end
  else if Sender = DivPatient5 then
  begin
    DivPatient5.ElementClassName := LClassSelected;
    LSelectedPatientIndex := 4;
  end
  else if Sender = DivPatient6 then
  begin
    DivPatient6.ElementClassName := LClassSelected;
    LSelectedPatientIndex := 5;
  end;

  //Nur wenn ein Patient effektiv gewählt wird tun wir was
  if LSelectedPatientIndex >= 0 then
  begin
    //Patient aus Liste holen
    SetPatient(DMMain.WartezimmerPatienten.Items[LSelectedPatientIndex]);

    if Assigned(FPatientInfo) then
    begin
      LabelPatientName.Caption := FPatientInfo.Patient.Name + ', ' + FPatientInfo.Patient.VorName;
      LabelVersicherung.Caption := FPatientInfo.Patient.Versicherung;
      LabelVersicherungstraeger.Caption := FPatientInfo.Patient.Versicherer;
      LabelBeruf.Caption := FPatientInfo.Patient.Beruf;
      LabelAufnahme.Caption := FPatientInfo.Patient.PatientSeit;
      LabelLetzterBesuch.Caption := FPatientInfo.Patient.LetzterBesuch;
      LabelArbeitgeber.Caption := FPatientInfo.Patient.Arbeitgeber;

      LabelBehandler.Caption := FPatientInfo.Behandler;
    end;
  end;
end;

procedure TFormWartezimmer.RefreshWartezimmer;
var
  LPatientInfo: TPatientInfo;

  i: integer;

begin
  await(DMMain.RefreshWartezimmer);

  //Leeres Wartezimmer initialisieren
  DivStammdaten.Visible := false;
  DivPatient1.Visible := false;
  DivPatient2.Visible := false;
  DivPatient3.Visible := false;
  DivPatient4.Visible := false;
  DivPatient5.Visible := false;
  DivPatient6.Visible := false;

  if DMMain.WartezimmerPatienten.Items.Count > 0 then
  begin

    for i := 1 to DMMain.WartezimmerPatienten.Items.Count do
    begin

      LPatientInfo := DMMain.WartezimmerPatienten.Items[i - 1];
      SetPatient(LPatientInfo);

      if Assigned(LPatientInfo) then
      begin

        if i = 1 then
        begin
          DivPatient1.Visible := true;
          LabelListePatient1Name.Caption := LPatientInfo.Patient.Name + ', ' + LPatientInfo.Patient.VorName;
          LabelListePatient1Info.Caption := '*' + LPatientInfo.Patient.Geburtstag + ', ' +
            LPatientInfo.Patient.Versicherer;
        end;

        if i = 2 then
        begin
          DivPatient2.Visible := true;
          LabelListePatient2Name.Caption := LPatientInfo.Patient.Name + ', ' + LPatientInfo.Patient.VorName;
          LabelListePatient2Info.Caption := '*' + LPatientInfo.Patient.Geburtstag + ', ' +
            LPatientInfo.Patient.Versicherer;
        end;

        if i = 3 then
        begin
          DivPatient3.Visible := true;
          LabelListePatient3Name.Caption := LPatientInfo.Patient.Name + ', ' + LPatientInfo.Patient.VorName;
          LabelListePatient3Info.Caption := '*' + LPatientInfo.Patient.Geburtstag + ', ' +
            LPatientInfo.Patient.Versicherer;
        end;

        if i = 4 then
        begin
          DivPatient4.Visible := true;
          LabelListePatient4Name.Caption := LPatientInfo.Patient.Name + ', ' + LPatientInfo.Patient.VorName;
          LabelListePatient4Info.Caption := '*' + LPatientInfo.Patient.Geburtstag + ', ' +
            LPatientInfo.Patient.Versicherer;
        end;

        if i = 5 then
        begin
          DivPatient5.Visible := true;
          LabelListePatient5Name.Caption := LPatientInfo.Patient.Name + ', ' + LPatientInfo.Patient.VorName;
          LabelListePatient5Info.Caption := '*' + LPatientInfo.Patient.Geburtstag + ', ' +
            LPatientInfo.Patient.Versicherer;
        end;

        if i = 6 then
        begin
          DivPatient6.Visible := true;
          LabelListePatient6Name.Caption := LPatientInfo.Patient.Name + ', ' + LPatientInfo.Patient.VorName;
          LabelListePatient6Info.Caption := '*' + LPatientInfo.Patient.Geburtstag + ', ' +
            LPatientInfo.Patient.Versicherer;
        end;

        DivStammdaten.Visible := true;
        //Max 6 Patienten zur Zeit
        if i = 6 then
          break;
      end;
    end;

    //Default 1. Patient ist ausgewählt
    if DMMain.WartezimmerPatienten.Items.Count > 0 then
    begin
      DivPatientClick(DivPatient1);
    end;

  end;
end;

procedure TFormWartezimmer.SetPatient(APatientInfo: TPatientInfo);
begin
  begin
    //Initialisieren
    FPatientInfo := nil;

    if Assigned(APatientInfo) then
    begin
      // Die PatientInfo einmal lokal
      FPatientInfo := APatientInfo;

      // Und einmal eine globale Referenz
      DMMain.AktuellerPatientInfo := FPatientInfo;
    end;
  end;
end;

procedure TFormWartezimmer.TimerWartezimmerTimer(Sender: TObject);
begin
  inherited;

  DMMain.RefreshWartezimmer(procedure
    begin
      DXLog('Refresh Wartezimmer');
      RefreshWartezimmer;
    end);
end;

procedure TFormWartezimmer.WebFormShow(Sender: TObject);
begin
  inherited;
  TimerWartezimmer.Enabled := true;
  RefreshWartezimmer;
end;

end.

