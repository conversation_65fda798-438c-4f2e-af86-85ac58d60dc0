﻿unit F01.Wartezimmer.Form;

interface

uses
  System.SysUtils, System.Classes,
  web,
  Vcl.Controls,
  WEBLib.Graphics, WEBLib.Controls, WEBLib.Forms, WEBLib.Dialogs,
  WEBLib.WebCtrls, Vcl.StdCtrls, WEBLib.StdCtrls, WEBLib.ExtCtrls,

  Base.Form.Status,
  Main.DM, Stammdaten.Patient;

type
  TFormWartezimmer = class(TFormBaseStatus)
    LabelPatientName: TWebLabel;
    DivStammdaten: TWebHTMLDiv;
    LabelVersicherungstraeger: TWebLabel;
    LabelVersicherung: TWebLabel;
    LabelBeruf: TWebLabel;
    LabelAufnahme: TWebLabel;
    LabelLetzterBesuch: TWebLabel;
    LabelArbeitgeber: TWebLabel;
    DivPatient1: TWebHTMLDiv;
    DivPatient2: TWebHTMLDiv;
    DivPatient3: TWebHTMLDiv;
    DivPatient4: TWebHTMLDiv;
    DivPatient5: TWebHTMLDiv;
    DivPatient6: TWebHTMLDiv;
    LabelListePatient1Name: TWebLabel;
    LabelListePatient1Info: TWebLabel;
    LabelListePatient2Info: TWebLabel;
    LabelListePatient2Name: TWebLabel;
    LabelListePatient3Info: TWebLabel;
    LabelListePatient3Name: TWebLabel;
    LabelListePatient4Info: TWebLabel;
    LabelListePatient4Name: TWebLabel;
    LabelListePatient5Info: TWebLabel;
    LabelListePatient5Name: TWebLabel;
    LabelListePatient6Info: TWebLabel;
    LabelListePatient6Name: TWebLabel;
    ButtonBehandlungStarten: TWebHTMLDiv;
    TimerWartezimmer: TWebTimer;
    procedure ButtonBehandlungstartenClick(Sender: TObject);
    procedure buttonLogoutClick(Sender: TObject);
    procedure DivPatientDblClick(Sender: TObject);
    procedure DivPatientClick(Sender: TObject);
    procedure TimerWartezimmerTimer(Sender: TObject);
    procedure WebFormShow(Sender: TObject);
  private
    FPatient: TPatient;

    LBeh, LNotiz, LPatNrStr, LName, LVorName, LGeburtstag, LStrasse, LPlz, LOrt, LTelefon, LEMail,
      LVersicherung, LVersicherer, LBeruf, LArbeitgeber, LPatientSeit, LLetzterBesuch, LZeitTermin: string;
    LDauerTermin, LPatNr: integer;

  protected
    [async]
    procedure RefreshWartezimmer;
    procedure SetPatient(APatientInfo: TPatientInfo);
  public
    { Public declarations }
  end;

implementation

uses
  App.Controller, DX.WEBLib.Logger;

{$R *.dfm}

procedure TFormWartezimmer.ButtonBehandlungstartenClick(Sender: TObject);
begin
  inherited;
  TimerWartezimmer.Enabled := false;
  AppController.ShowFormUebersicht(FPatient);
  DXLog('Übersicht ...');
  Close;
end;

procedure TFormWartezimmer.buttonLogoutClick(Sender: TObject);
begin
  AppController.Logout;
end;

procedure TFormWartezimmer.DivPatientDblClick(Sender: TObject);
begin
  inherited;
  ButtonBehandlungstartenClick(Sender);
end;

procedure TFormWartezimmer.DivPatientClick(Sender: TObject);
var
  LClassSelected: string;
  LCLassUnselected: string;
begin
  inherited;
  LClassSelected := 'two-line-list-2 two-line-list-3';
  LCLassUnselected := 'two-line-list';

  DivPatient1.ElementClassName := LCLassUnselected;
  DivPatient2.ElementClassName := LCLassUnselected;
  DivPatient3.ElementClassName := LCLassUnselected;
  DivPatient4.ElementClassName := LCLassUnselected;
  DivPatient5.ElementClassName := LCLassUnselected;
  DivPatient6.ElementClassName := LCLassUnselected;

  if Sender = DivPatient1 then
  begin
    DivPatient1.ElementClassName := LClassSelected;
  end
  else if Sender = DivPatient2 then
  begin
    DivPatient2.ElementClassName := LClassSelected;
  end
  else if Sender = DivPatient3 then
  begin
    DivPatient3.ElementClassName := LClassSelected;
  end
  else if Sender = DivPatient4 then
  begin
    DivPatient4.ElementClassName := LClassSelected;
  end
  else if Sender = DivPatient5 then
  begin
    DivPatient5.ElementClassName := LClassSelected;
  end
  else if Sender = DivPatient6 then
  begin
    DivPatient6.ElementClassName := LClassSelected;
  end;

  SetPatient(DMMain.WartezimmerPatienten.Items.First);

 if not Assigned(FPatient) then
  begin
    LabelPatientName.Caption := '';
    LabelVersicherung.Caption := '';
    LabelVersicherungstraeger.Caption := '';
    LabelBeruf.Caption := '';
    LabelAufnahme.Caption := '';
    LabelLetzterBesuch.Caption := '';
    LabelArbeitgeber.Caption := '';
    Exit;
  end;

  LabelPatientName.Caption := FPatient.Name + ', ' + FPatient.VorName;
  LabelVersicherung.Caption := FPatient.Versicherung;
  LabelVersicherungstraeger.Caption := FPatient.Versicherer;
  LabelBeruf.Caption := FPatient.Beruf;
  LabelAufnahme.Caption := FPatient.PatientSeit;
  LabelLetzterBesuch.Caption := FPatient.LetzterBesuch;
  LabelArbeitgeber.Caption := FPatient.Arbeitgeber;

end;

procedure TFormWartezimmer.RefreshWartezimmer;
var
  LPatientInfo: TPatientInfo;

  i: integer;

begin
  await(DMMain.RefreshWartezimmer);
  if DMMain.WartezimmerPatienten.Items.Count = 0 then
  begin
    DivStammdaten.Visible := false;
  end
  else
  begin
    DivStammdaten.Visible := false;

    DivPatient1.Visible := false;
    DivPatient2.Visible := false;
    DivPatient3.Visible := false;
    DivPatient4.Visible := false;
    DivPatient5.Visible := false;
    DivPatient6.Visible := false;

    for i := 1 to DMMain.WartezimmerPatienten.Items.Count do
    begin

      LPatientInfo := DMMain.WartezimmerPatienten.Items[i - 1];
      SetPatient(LPatientInfo);

      if Assigned(LPatientInfo) then
      begin

        if i = 1 then
        begin
          DivPatient1.Visible := true;
          LabelListePatient1Name.Caption := LName + ', ' + LVorName;
          LabelListePatient1Info.Caption := '*' + LGeburtstag + ', ' + LVersicherer;
        end;

        if i = 2 then
        begin
          DivPatient2.Visible := true;
          LabelListePatient2Name.Caption := LName + ', ' + LVorName;
          LabelListePatient2Info.Caption := '*' + LGeburtstag + ', ' + LVersicherer;
        end;

        if i = 3 then
        begin
          DivPatient3.Visible := true;
          LabelListePatient3Name.Caption := LName + ', ' + LVorName;
          LabelListePatient3Info.Caption := '*' + LGeburtstag + ', ' + LVersicherer;
        end;

        if i = 4 then
        begin
          DivPatient4.Visible := true;
          LabelListePatient4Name.Caption := LName + ', ' + LVorName;
          LabelListePatient4Info.Caption := '*' + LGeburtstag + ', ' + LVersicherer;
        end;

        if i = 5 then
        begin
          DivPatient5.Visible := true;
          LabelListePatient5Name.Caption := LName + ', ' + LVorName;
          LabelListePatient5Info.Caption := '*' + LGeburtstag + ', ' + LVersicherer;
        end;

        if i = 6 then
        begin
          DivPatient6.Visible := true;
          LabelListePatient6Name.Caption := LName + ', ' + LVorName;
          LabelListePatient6Info.Caption := '*' + LGeburtstag + ', ' + LVersicherer;
        end;

        DivStammdaten.Visible := true;
        //Max 6 Patienten zur Zeit
        if i = 6 then
          break;
      end;
    end;

    //Default 1. Patient ist ausgewählt
    if DMMain.WartezimmerPatienten.Items.Count > 0 then
    begin
      DivPatientClick(DivPatient1);
    end;

  end;
end;

procedure TFormWartezimmer.SetPatient(APatientInfo: TPatientInfo);
begin
  begin

    //Initialisieren
    FPatient := nil;

    LBeh := '';
    LDauerTermin := 0;
    LNotiz := '';
    LZeitTermin := '';

    LPatNr := 0;
    LPatNrStr := '';
    LName := '';
    LVorName := '';
    LGeburtstag := '';
    LStrasse := '';
    LPlz := '';
    LOrt := '';
    LTelefon := '';
    LEMail := '';
    LVersicherung := '';
    LVersicherer := '';
    LBeruf := '';
    LArbeitgeber := '';
    LPatientSeit := '';
    LLetzterBesuch := '';

    if Assigned(APatientInfo) then
    begin
      // Wartezimmer-Eigenschaften
      LBeh := APatientInfo.Beh;
      LDauerTermin := APatientInfo.DauerTermin;
      LNotiz := APatientInfo.Notiz;
      LZeitTermin := APatientInfo.ZeitTermin;

      // Der Patient
      FPatient := APatientInfo.Patient;

      // Zuweisung an z.B. eine globale Referenz
      DMMain.AktuellerPatient := FPatient;

      // Eigenschaften des Patient-Objekts
      LPatNr := FPatient.PatNr;
      LPatNrStr := IntToStr(LPatNr);
      LName := FPatient.Name;
      LVorName := FPatient.VorName;
      LGeburtstag := FPatient.Geburtstag;
      LStrasse := FPatient.Strasse;
      LPlz := FPatient.Plz;
      LOrt := FPatient.Ort;
      LTelefon := FPatient.Telefon;
      LEMail := FPatient.EMail;
      LVersicherung := FPatient.Versicherung;
      LVersicherer := FPatient.Versicherer;
      LBeruf := FPatient.Beruf;
      LArbeitgeber := FPatient.Arbeitgeber;
      LPatientSeit := FPatient.PatientSeit;
      LLetzterBesuch := FPatient.LetzterBesuch;
    end;
  end;
end;

procedure TFormWartezimmer.TimerWartezimmerTimer(Sender: TObject);
begin
  inherited;

  DMMain.RefreshWartezimmer(procedure
    begin
      DXLog('Refresh Wartezimmer');
      RefreshWartezimmer;
    end);
end;

procedure TFormWartezimmer.WebFormShow(Sender: TObject);
begin
  inherited;
  TimerWartezimmer.Enabled := true;
  RefreshWartezimmer;
end;

end.

