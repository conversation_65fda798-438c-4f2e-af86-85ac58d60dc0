﻿unit F00.Login.Form;

interface

uses
  System.SysUtils, System.Classes,

  JS, Web,

  WEBLib.Graphics, WEBLib.Controls, WEBLib.Forms, WEBLib.Dialogs, Vcl.Controls, WEBLib.Login, WEBLib.StdCtrls,
  WEBLib.ExtCtrls, WEBLib.WebCtrls,

  Vcl.StdCtrls,

  Base.Form, Base.Form.Status;

type
  TFormLogin = class(TFormBaseStatus)
    EditPassword: TWebEdit;
    ButtonStart: TWebButton;
    Dialog: TWebMessageDlg;
    EditUser: TWebEdit;
    ButtonTest: TWebButton;
    procedure WebFormCreate(Sender: TObject);
    [async]
    procedure ButtonLoginClick(Sender: TObject);
    procedure ButtonTestClick(Sender: TObject);
    procedure EditPasswordKeyDown(Sender: TObject; var Key: Word; Shift: TShiftState);
  private
    { Private-Deklarationen }
  public
    { Public-Deklarationen }
  end;

var
  FormLogin: TFormLogin;

implementation

uses
  App.Controller,
  Main.DM,
  F01.Wartezimmer.Form;

{$R *.dfm}

procedure TFormLogin.WebFormCreate(Sender: TObject);
begin
  Title := 'Login';
  inherited;
{$IFDEF DEBUG}
  //EditUser.Text := 'test';
  //EditPassword.Text := 'test';
  ButtonTest.Visible := true;
{$ELSE}
  ButtonTest.Visible := false;
{$ENDIF DEBUG}
  EditPassword.SetFocus;
end;

procedure TFormLogin.ButtonLoginClick(Sender: TObject);
begin
  inherited;
  try
    await(DMMain.Login(EditUser.Text, EditPassword.Text));
    AppController.ShowWartezimmer;
    close;
  except

    (*
    Dialog.ShowDialog('Login fehlerhaft', mtError, [mbOK],
      procedure(AValue: TModalResult)
      begin
        EditPassword.SetFocus;
      end);
    *)

    MessageDlg('Login fehlerhaft', mtError, [mbOK],
      procedure(AValue: TModalResult)
      begin
        EditPassword.SetFocus;
      end);

  end;
end;

procedure TFormLogin.ButtonTestClick(Sender: TObject);
begin
  inherited;
{$IFDEF DEBUG}
  //Quick login im Debug mode
  EditUser.Text := 'test';
  EditPassword.Text := 'test';
  ButtonLoginClick(Sender);
  //AppController.ShowBehandlungBefundTest;
{$ENDIF}
end;

procedure TFormLogin.EditPasswordKeyDown(Sender: TObject; var Key: Word; Shift: TShiftState);
begin
  inherited;
  if Key = VK_RETURN then
  begin
    ButtonStart.Click;
  end;
end;

end.

