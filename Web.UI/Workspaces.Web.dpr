﻿program Workspaces.Web;

{$R *.dres}

uses
  Vcl.Forms,
  WEBLib.Forms,
  DX.WebLib.XData in '..\libs\dx-library\DX.WebLib.XData.pas',
  DX.WEBLib.Config in '..\libs\dx-library\DX.WEBLib.Config.pas',
  DX.WEBLib.Logger in '..\libs\dx-library\DX.WEBLib.Logger.pas',
  DX.WEBLib.SysUtils in '..\libs\dx-library\DX.WEBLib.SysUtils.pas',
  DX.WEBLib.Tabulator in '..\libs\dx-library\DX.WEBLib.Tabulator.pas',
  Base.Form in 'Base.Form.pas' {FormBase: TWebForm},
  Base.Form.Status in 'Base.Form.Status.pas' {FormBaseStatus: TWebForm},
  App.Controller in 'App.Controller.pas',
  Main.DM in 'Main.DM.pas' {DMMain: TWebDataModule},
  F00.Login.Form in 'F00.Login.Form.pas' {FormLogin: TWebForm} {*.html},
  F01.Wartezimmer.Form in 'F01.Wartezimmer.Form.pas' {FormWartezimmer: TWebForm} {*.html},
  F02.Uebersicht.Form in 'F02.Uebersicht.Form.pas' {FormUebersicht: TWebForm} {*.html},
  F04.Test.Behandlung.Befund.Form in 'F04.Test.Behandlung.Befund.Form.pas' {FormBehandlungBefundTest: TWebForm} {*.html},
  Unit2 in 'Unit2.pas' {Form1: TWebForm} {*.html},
  Stammdaten.Patient in 'Model\Stammdaten.Patient.pas',
  F04.Behandlung.Befund.Form in 'F04.Behandlung.Befund.Form.pas' {FormBehandlungBefund: TWebForm} {*.html},
  Zahnschema.Frame in 'Zahnschema.Frame.pas' {FrameZahnschema: TWebFrame} {*.html};

{$R *.res}

begin
  Application.Initialize;
  Application.MainFormOnTaskbar := True;
  Application.Routing := true;
  Application.AutoFormRoute := true;
  Application.CreateForm(TDMMain, DMMain);
  Application.CreateForm(TFormLogin, FormLogin);
  Application.Run;

end.

