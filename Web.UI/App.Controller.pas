﻿unit App.Controller;

interface
uses
  System.SysUtils, System.Classes,
  JS,
  WEBLib.Forms,
  DX.WEBLib.Logger,

  F00.Login.Form, F02.Uebersicht.Form, F01.Wartezimmer.Form, F04.Behandlung.Befund.Form,

  Stammdaten.Patient, F04.Test.Behandlung.Befund.Form, Main.DM;

type
  TAppController = class(TObject)
  private
    class var FInstance: TAppController;
  private
    FAppTitle: string;
    FFormBehandlungBefund: TFormBehandlungBefund;
    FFormUebersicht: TFormUebersicht;
    FFormWarteZimmer: TFormWartezimmer;
  public
    constructor Create;
    property AppTitle: string read FAppTitle;
    property FormWarteZimmer: TFormWartezimmer read FFormWarteZimmer;
    property FormUebersicht: TFormUebersicht read FFormUebersicht;
    property FormBehandlungBefund: TFormBehandlungBefund read FFormBehandlungBefund;
  public
    function ShowForm<T: TWebForm>(AFormCreatedProc: TProc<T> = nil): T;
    procedure ShowWartezimmer;
    procedure ShowFormUebersicht(APatientInfo: TPatientInfo);
    procedure ShowBehandlungBefund(APatientInfo: TPatientInfo);
    procedure Logout;

    //Test only
    procedure ShowBehandlungBefundTest;

  end;

function AppController: TAppController;

const
  APP_TITLE = 'EVIDENT Workspaces';

implementation

function AppController: TAppController;
begin
  if TAppController.FInstance = nil then
  begin
    TAppController.FInstance := TAppController.Create;
  end;
  Result := TAppController.FInstance;
end;

function TAppController.ShowForm<T>(AFormCreatedProc: TProc<T> = nil): T;
begin
  DXLog('Show form ' + T.classname);
  Result := T.CreateNew(
    procedure(AForm: TObject)
    begin
      TWebForm(AForm).Popup := false;
      TWebForm(AForm).Border := fbSingle;
      if Assigned(AFormCreatedProc) then
      begin
        AFormCreatedProc(T(AForm));
      end;
      TWebForm(AForm).ShowModal;
    end);
end;

procedure TAppController.ShowWartezimmer;
begin
  DXLog('ShowWartezimmer');
  if Assigned(FFormWarteZimmer) then
  begin
    FFormWarteZimmer.ShowModal;
  end
  else
  begin
    FFormWarteZimmer := ShowForm<TFormWartezimmer>;
  end;
end;

procedure TAppController.ShowFormUebersicht(APatientInfo: TPatientInfo);
begin
  DXLog('ShowFormUebersicht');
  FFormUebersicht := ShowForm<TFormUebersicht>(procedure(AForm: TFormUebersicht)
    begin
      AForm.PatientInfo := APatientInfo;
    end);
end;

procedure TAppController.ShowBehandlungBefund(APatientInfo: TPatientInfo);
begin
  FFormBehandlungBefund := ShowForm<TFormBehandlungBefund>(procedure(AForm: TFormBehandlungBefund)
    begin
      AForm.PatientInfo := APatientInfo;
    end);
end;

procedure TAppController.ShowBehandlungBefundTest;
begin
  DMMain.GetFirstPatientFromWartezimmer(procedure(APatientInfo: TPatientInfo)
    begin
      ShowForm<TFormBehandlungBefundTest>(procedure(AForm: TFormBehandlungBefundTest)
        begin
          AForm.PatientInfo := APatientInfo;
        end);
    end);
end;

constructor TAppController.Create;
begin
  inherited;
  FAppTitle := APP_TITLE;
end;

procedure TAppController.Logout;
begin
  ShowForm<TFormLogin>;
end;

end.

