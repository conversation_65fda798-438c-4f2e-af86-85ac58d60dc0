﻿<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
    <PropertyGroup>
        <ProjectGuid>{786EFAD2-B24C-4A2E-99BF-757FCA6A116E}</ProjectGuid>
    </PropertyGroup>
    <ItemGroup>
        <Projects Include="Workspaces.Web.dproj">
            <Dependencies/>
        </Projects>
        <Projects Include="WebCoreTest\WebCoreTest.dproj">
            <Dependencies/>
        </Projects>
    </ItemGroup>
    <ProjectExtensions>
        <Borland.Personality>Default.Personality.12</Borland.Personality>
        <Borland.ProjectType/>
        <BorlandProject>
            <Default.Personality/>
        </BorlandProject>
    </ProjectExtensions>
    <Target Name="Workspaces_Web">
        <MSBuild Projects="Workspaces.Web.dproj"/>
    </Target>
    <Target Name="Workspaces_Web:Clean">
        <MSBuild Projects="Workspaces.Web.dproj" Targets="Clean"/>
    </Target>
    <Target Name="Workspaces_Web:Make">
        <MSBuild Projects="Workspaces.Web.dproj" Targets="Make"/>
    </Target>
    <Target Name="WebCoreTest">
        <MSBuild Projects="WebCoreTest\WebCoreTest.dproj"/>
    </Target>
    <Target Name="WebCoreTest:Clean">
        <MSBuild Projects="WebCoreTest\WebCoreTest.dproj" Targets="Clean"/>
    </Target>
    <Target Name="WebCoreTest:Make">
        <MSBuild Projects="WebCoreTest\WebCoreTest.dproj" Targets="Make"/>
    </Target>
    <Target Name="Build">
        <CallTarget Targets="Workspaces_Web;WebCoreTest"/>
    </Target>
    <Target Name="Clean">
        <CallTarget Targets="Workspaces_Web:Clean;WebCoreTest:Clean"/>
    </Target>
    <Target Name="Make">
        <CallTarget Targets="Workspaces_Web:Make;WebCoreTest:Make"/>
    </Target>
    <Import Project="$(BDS)\Bin\CodeGear.Group.Targets" Condition="Exists('$(BDS)\Bin\CodeGear.Group.Targets')"/>
</Project>
