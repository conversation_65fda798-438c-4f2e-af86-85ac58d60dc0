﻿unit Base.Form.Status;

interface

uses
  System.SysUtils, System.Variants, System.Classes, System.DateUtils,
  Vcl.Graphics, Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.StdCtrls, Vcl.Menus, Vcl.Imaging.pngimage,
  WebLib.Forms, WebLib.StdCtrls, WebLib.Controls, WebLib.ExtCtrls, WebLib.Menus, WebLib.Buttons,
  DX.WebLib.SysUtils, DX.WebLib.Logger, DX.WebLib.Config,
  Base.Form;

type
  /// <summary>
  /// Vorlage für die "Stand-alone" Formulare der App.
  /// Das Form hat eine Statusbar
  /// </summary>
  TFormBaseStatus = class abstract(TFormBase)
    PanelStatus: TWebPanel;
    LabelStatus: TWebLabel;
    LabelDebug: TWebLabel;
    procedure WebFormCreate(Sender: TObject);
    procedure WebFormResize(Sender: TObject);
  private
    procedure UpdateStatus;
    { Private-Deklarationen }
  public
    { Public-Deklarationen }
  end;

implementation

{$R *.dfm}

procedure TFormBaseStatus.WebFormCreate(Sender: TObject);
var
  LPanelHeight: Integer;
begin
  inherited;
{$IFDEF DEBUG}
  LabelDebug.Visible := true;
{$ELSE}
  LabelDebug.Visible := false;
{$ENDIF}

  //Höhe des App Containers korrigieren
  LPanelHeight:= PanelStatus.Height;
  Assert(LPanelHeight >= 0);
  {$IFDEF PAS2JS}
  asm
      document.getElementById("app-container").style.height = "calc(100vh - " + LPanelHeight + "px)";
  end;
  {$ENDIF}
end;

procedure TFormBaseStatus.UpdateStatus;
var
  LResolution: string;
begin
  LResolution := Format('%dx%d',[self.Width, self.Height]);
  LabelStatus.Caption := Format('EVIDENT Workspaces • © %d EVIDENT GmbH • Version %s • %s • Screen: %s',
    [YearOf(Today), TAppInfo.VersionLong, TAppInfo.IsPwa('Standalone-Modus', 'Browser-Modus'),
      LResolution]);
end;

procedure TFormBaseStatus.WebFormResize(Sender: TObject);
begin
  // Resize wird beim Erstellen des Formulars, beim Neuausrichten und bei Änderungen der Größe des Client-Bereichs aufgerufen.
  // Auf diese Weise erhalten wir auch ein Ereignis, wenn die App vom Browser in den eigenständigen PWA-Modus wechselt.
  UpdateStatus;
end;

end.

