unit Stammdaten.Dauerdiagnosen;

interface

uses
  System.SysUtils, System.Classes,
  Generics.Collections,
  JS, Web,
  Aurelius.Types.Nullable;

type
  /// <summary>
  /// DTO-Klasse für Dauerdiagnosen - eigenständige Implementierung ohne ORM-Abhängigkeiten
  /// </summary>
  TDauerdiagnosen = class
  private
    // ORM-Properties als eigenständige Felder (kopiert aus TDauerdiagnosen ORM-Klasse)
    FPatnr: Integer;
    FQuartal: string;
    FAnamnese: string;
    FReihenfolge: Integer;
    FIcdCode: Nullable<string>;
    FDiagnosensicherheit: Nullable<string>;
    FSeitenlokalisation: Nullable<string>;
    FDiagnosenerlaeuterung: string;
    FDiagnoseausnahme: string;
    FAusgewaehlt: Nullable<string>;
    FLeereintrag: Nullable<string>;

    // Zusätzliche DTO-spezifische Felder
    FDisplayText: string;
    FIsSelected: Boolean;
    FIcdCodeText: string;
    
  public
    constructor Create;
    destructor Destroy; override;
    
    /// <summary>
    /// Lädt die Dauerdiagnose aus einem JavaScript-Objekt
    /// </summary>
    procedure LoadFromJSObject(const ADauerdiagnose: TJSObject);
    
    /// <summary>
    /// Konvertiert die Dauerdiagnose zu einem JavaScript-Objekt
    /// </summary>
    function ToJSObject: TJSObject;
    
    /// <summary>
    /// Erstellt einen Anzeigetext für die Dauerdiagnose
    /// </summary>
    function GetDisplayText: string;
    
    /// <summary>
    /// Prüft, ob die Dauerdiagnose gültig ist
    /// </summary>
    function IsValid: Boolean;
    
    /// <summary>
    /// Erstellt eine Kopie der Dauerdiagnose
    /// </summary>
    function Clone: TDauerdiagnosen;

    // ORM-Properties (kopiert aus der ORM-Klasse)
    /// <summary>
    /// Patientennummer
    /// </summary>
    property Patnr: Integer read FPatnr write FPatnr;

    /// <summary>
    /// Quartal (Format: JJJJQ, z.B. "20241")
    /// </summary>
    property Quartal: string read FQuartal write FQuartal;

    /// <summary>
    /// Anamnese-Kennzeichen
    /// </summary>
    property Anamnese: string read FAnamnese write FAnamnese;

    /// <summary>
    /// Reihenfolge der Diagnose
    /// </summary>
    property Reihenfolge: Integer read FReihenfolge write FReihenfolge;

    /// <summary>
    /// ICD-Code der Diagnose
    /// </summary>
    property IcdCode: Nullable<string> read FIcdCode write FIcdCode;

    /// <summary>
    /// Diagnosensicherheit (G=Gesichert, V=Verdacht, Z=Zustand nach, A=Ausschluss)
    /// </summary>
    property Diagnosensicherheit: Nullable<string> read FDiagnosensicherheit write FDiagnosensicherheit;

    /// <summary>
    /// Seitenlokalisation (R=Rechts, L=Links, B=Beidseitig)
    /// </summary>
    property Seitenlokalisation: Nullable<string> read FSeitenlokalisation write FSeitenlokalisation;

    /// <summary>
    /// Erläuterung zur Diagnose
    /// </summary>
    property Diagnosenerlaeuterung: string read FDiagnosenerlaeuterung write FDiagnosenerlaeuterung;

    /// <summary>
    /// Ausnahme zur Diagnose
    /// </summary>
    property Diagnoseausnahme: string read FDiagnoseausnahme write FDiagnoseausnahme;

    /// <summary>
    /// Ist die Diagnose ausgewählt (J/N)
    /// </summary>
    property Ausgewaehlt: Nullable<string> read FAusgewaehlt write FAusgewaehlt;

    /// <summary>
    /// Ist es ein Leereintrag (J/N)
    /// </summary>
    property Leereintrag: Nullable<string> read FLeereintrag write FLeereintrag;

    // DTO-spezifische Properties
    /// <summary>
    /// Anzeigetext für die UI
    /// </summary>
    property DisplayText: string read FDisplayText write FDisplayText;

    /// <summary>
    /// Gibt an, ob die Dauerdiagnose ausgewählt ist
    /// </summary>
    property IsSelected: Boolean read FIsSelected write FIsSelected;

    /// <summary>
    /// Beschreibungstext für den ICD-Code
    /// </summary>
    property IcdCodeText: string read FIcdCodeText write FIcdCodeText;
  end;

  /// <summary>
  /// Liste von Dauerdiagnosen
  /// </summary>
  TDauerdiagnosenList = class
  private
    FItems: TObjectList<TDauerdiagnosen>;
  public
    constructor Create;
    destructor Destroy; override;
    
    /// <summary>
    /// Lädt die Liste aus einem JavaScript-Array
    /// </summary>
    procedure LoadFromJSArray(const JSONArray: TJSArray);
    
    /// <summary>
    /// Konvertiert die Liste zu einem JavaScript-Array
    /// </summary>
    function ToJSArray: TJSArray;
    
    /// <summary>
    /// Fügt eine Dauerdiagnose hinzu
    /// </summary>
    procedure Add(ADauerdiagnose: TDauerdiagnosen);
    
    /// <summary>
    /// Entfernt eine Dauerdiagnose
    /// </summary>
    procedure Remove(ADauerdiagnose: TDauerdiagnosen);
    
    /// <summary>
    /// Leert die Liste
    /// </summary>
    procedure Clear;
    
    /// <summary>
    /// Gibt die Anzahl der Elemente zurück
    /// </summary>
    function Count: Integer;
    
    /// <summary>
    /// Findet eine Dauerdiagnose anhand der Patientennummer und Reihenfolge
    /// </summary>
    function FindByPatnrAndReihenfolge(APatnr: Integer; AReihenfolge: Integer): TDauerdiagnosen;
    
    /// <summary>
    /// Gibt alle ausgewählten Dauerdiagnosen zurück
    /// </summary>
    function GetSelected: TArray<TDauerdiagnosen>;
    
    /// <summary>
    /// Liste der Dauerdiagnosen
    /// </summary>
    property Items: TObjectList<TDauerdiagnosen> read FItems;
  end;

implementation

{ TDauerdiagnosen }

constructor TDauerdiagnosen.Create;
begin
  inherited Create;

  // ORM-Felder initialisieren
  FPatnr := 0;
  FQuartal := '';
  FAnamnese := '';
  FReihenfolge := 0;
  FIcdCode := Nullable<string>.Null;
  FDiagnosensicherheit := Nullable<string>.Null;
  FSeitenlokalisation := Nullable<string>.Null;
  FDiagnosenerlaeuterung := '';
  FDiagnoseausnahme := '';
  FAusgewaehlt := Nullable<string>.Null;
  FLeereintrag := Nullable<string>.Null;

  // DTO-spezifische Felder initialisieren
  FDisplayText := '';
  FIsSelected := False;
  FIcdCodeText := '';
end;

destructor TDauerdiagnosen.Destroy;
begin
  inherited Destroy;
end;

procedure TDauerdiagnosen.LoadFromJSObject(const ADauerdiagnose: TJSObject);
begin
  if not Assigned(ADauerdiagnose) then
    Exit;

  // ORM-Properties laden
  FPatnr := StrToIntDef(string(ADauerdiagnose['Patnr']), 0);
  FQuartal := string(ADauerdiagnose['Quartal']);
  FAnamnese := string(ADauerdiagnose['Anamnese']);
  FReihenfolge := StrToIntDef(string(ADauerdiagnose['Reihenfolge']), 0);

  if ADauerdiagnose['IcdCode'] <> undefined then
    FIcdCode := string(ADauerdiagnose['IcdCode'])
  else
    FIcdCode := Nullable<string>.Null;

  if ADauerdiagnose['Diagnosensicherheit'] <> undefined then
    FDiagnosensicherheit := string(ADauerdiagnose['Diagnosensicherheit'])
  else
    FDiagnosensicherheit := Nullable<string>.Null;

  if ADauerdiagnose['Seitenlokalisation'] <> undefined then
    FSeitenlokalisation := string(ADauerdiagnose['Seitenlokalisation'])
  else
    FSeitenlokalisation := Nullable<string>.Null;

  if ADauerdiagnose['Diagnosenerlaeuterung'] <> undefined then
    FDiagnosenerlaeuterung := string(ADauerdiagnose['Diagnosenerlaeuterung'])
  else
    FDiagnosenerlaeuterung := '';

  if ADauerdiagnose['Diagnoseausnahme'] <> undefined then
    FDiagnoseausnahme := string(ADauerdiagnose['Diagnoseausnahme'])
  else
    FDiagnoseausnahme := '';

  if ADauerdiagnose['Ausgewaehlt'] <> undefined then
    FAusgewaehlt := string(ADauerdiagnose['Ausgewaehlt'])
  else
    FAusgewaehlt := Nullable<string>.Null;

  if ADauerdiagnose['Leereintrag'] <> undefined then
    FLeereintrag := string(ADauerdiagnose['Leereintrag'])
  else
    FLeereintrag := Nullable<string>.Null;

  // DTO-spezifische Properties
  if ADauerdiagnose['DisplayText'] <> undefined then
    FDisplayText := string(ADauerdiagnose['DisplayText'])
  else
    FDisplayText := '';

  if ADauerdiagnose['IsSelected'] <> undefined then
    FIsSelected := Boolean(ADauerdiagnose['IsSelected'])
  else
    FIsSelected := False;

  if ADauerdiagnose['IcdCodeText'] <> undefined then
    FIcdCodeText := string(ADauerdiagnose['IcdCodeText'])
  else
    FIcdCodeText := '';
end;

function TDauerdiagnosen.ToJSObject: TJSObject;
begin
  Result := TJSObject.new;

  // ORM-Properties
  Result['Patnr'] := FPatnr;
  Result['Quartal'] := FQuartal;
  Result['Anamnese'] := FAnamnese;
  Result['Reihenfolge'] := FReihenfolge;

  if FIcdCode.HasValue then
    Result['IcdCode'] := FIcdCode.Value;

  if FDiagnosensicherheit.HasValue then
    Result['Diagnosensicherheit'] := FDiagnosensicherheit.Value;

  if FSeitenlokalisation.HasValue then
    Result['Seitenlokalisation'] := FSeitenlokalisation.Value;

  if FDiagnosenerlaeuterung <> '' then
    Result['Diagnosenerlaeuterung'] := FDiagnosenerlaeuterung;

  if FDiagnoseausnahme <> '' then
    Result['Diagnoseausnahme'] := FDiagnoseausnahme;

  if FAusgewaehlt.HasValue then
    Result['Ausgewaehlt'] := FAusgewaehlt.Value;

  if FLeereintrag.HasValue then
    Result['Leereintrag'] := FLeereintrag.Value;

  // DTO-spezifische Properties
  Result['DisplayText'] := FDisplayText;
  Result['IsSelected'] := FIsSelected;
  Result['IcdCodeText'] := FIcdCodeText;
end;

function TDauerdiagnosen.GetDisplayText: string;
begin
  if FDisplayText <> '' then
    Result := FDisplayText
  else
  begin
    Result := '';
    if FIcdCode.HasValue and (FIcdCode.Value <> '') then
      Result := FIcdCode.Value;

    if FIcdCodeText <> '' then
    begin
      if Result <> '' then
        Result := Result + ' - ' + FIcdCodeText
      else
        Result := FIcdCodeText;
    end;

    if Result = '' then
      Result := 'Dauerdiagnose #' + IntToStr(FReihenfolge);
  end;
end;

function TDauerdiagnosen.IsValid: Boolean;
begin
  Result := (FPatnr > 0) and (FQuartal <> '') and (FAnamnese <> '') and (FReihenfolge >= 0);
end;

function TDauerdiagnosen.Clone: TDauerdiagnosen;
begin
  Result := TDauerdiagnosen.Create;
  
  // Basis-Properties kopieren
  Result.Patnr := Self.Patnr;
  Result.Quartal := Self.Quartal;
  Result.Anamnese := Self.Anamnese;
  Result.Reihenfolge := Self.Reihenfolge;
  Result.IcdCode := Self.IcdCode;
  Result.Diagnosensicherheit := Self.Diagnosensicherheit;
  Result.Seitenlokalisation := Self.Seitenlokalisation;
  Result.Ausgewaehlt := Self.Ausgewaehlt;
  Result.Leereintrag := Self.Leereintrag;
  
  // DTO-spezifische Properties kopieren
  Result.FDisplayText := Self.FDisplayText;
  Result.FIsSelected := Self.FIsSelected;
  Result.FIcdCodeText := Self.FIcdCodeText;
end;

{ TDauerdiagnosenList }

constructor TDauerdiagnosenList.Create;
begin
  inherited Create;
  FItems := TObjectList<TDauerdiagnosen>.Create(True);
end;

destructor TDauerdiagnosenList.Destroy;
begin
  FItems.Free;
  inherited Destroy;
end;

procedure TDauerdiagnosenList.LoadFromJSArray(const JSONArray: TJSArray);
var
  i: Integer;
  LDauerdiagnose: TDauerdiagnosen;
begin
  FItems.Clear;
  
  if not Assigned(JSONArray) then
    Exit;
    
  for i := 0 to JSONArray.Length - 1 do
  begin
    LDauerdiagnose := TDauerdiagnosen.Create;
    LDauerdiagnose.LoadFromJSObject(TJSObject(JSONArray[i]));
    FItems.Add(LDauerdiagnose);
  end;
end;

function TDauerdiagnosenList.ToJSArray: TJSArray;
var
  i: Integer;
begin
  Result := TJSArray.new;
  
  for i := 0 to FItems.Count - 1 do
  begin
    Result.push(FItems[i].ToJSObject);
  end;
end;

procedure TDauerdiagnosenList.Add(ADauerdiagnose: TDauerdiagnosen);
begin
  if Assigned(ADauerdiagnose) then
    FItems.Add(ADauerdiagnose);
end;

procedure TDauerdiagnosenList.Remove(ADauerdiagnose: TDauerdiagnosen);
begin
  if Assigned(ADauerdiagnose) then
    FItems.Remove(ADauerdiagnose);
end;

procedure TDauerdiagnosenList.Clear;
begin
  FItems.Clear;
end;

function TDauerdiagnosenList.Count: Integer;
begin
  Result := FItems.Count;
end;

function TDauerdiagnosenList.FindByPatnrAndReihenfolge(APatnr: Integer; AReihenfolge: Integer): TDauerdiagnosen;
var
  i: Integer;
begin
  Result := nil;
  
  for i := 0 to FItems.Count - 1 do
  begin
    if (FItems[i].Patnr = APatnr) and (FItems[i].Reihenfolge = AReihenfolge) then
    begin
      Result := FItems[i];
      Break;
    end;
  end;
end;

function TDauerdiagnosenList.GetSelected: TArray<TDauerdiagnosen>;
var
  i: Integer;
  LSelected: TList<TDauerdiagnosen>;
begin
  LSelected := TList<TDauerdiagnosen>.Create;
  try
    for i := 0 to FItems.Count - 1 do
    begin
      if FItems[i].IsSelected or 
         (FItems[i].Ausgewaehlt.HasValue and (FItems[i].Ausgewaehlt.Value = 'J')) then
        LSelected.Add(FItems[i]);
    end;
    
    Result := LSelected.ToArray;
  finally
    LSelected.Free;
  end;
end;

end.
