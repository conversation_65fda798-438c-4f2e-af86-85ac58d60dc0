unit Stammdaten.Dauerdiagnosen;

interface

uses
  System.SysUtils, System.Classes,
  Generics.Collections,
  JS, Web,
  Aurelius.Types.Nullable,
  Aurelius.Types.Blob,
  Evident.Classes.Praxis.Generated;

type
  /// <summary>
  /// DTO-Klasse für Dauerdiagnosen, abgeleitet von der ORM-Klasse TDauerdiagnosen
  /// </summary>
  TDauerdiagnosen = class(Evident.Classes.Praxis.Generated.TDauerdiagnosen)
  private
    // Zusätzliche DTO-spezifische Felder
    FDisplayText: string;
    FIsSelected: Boolean;
    FIcdCodeText: string;
    
  public
    constructor Create;
    destructor Destroy; override;
    
    /// <summary>
    /// Lädt die Dauerdiagnose aus einem JavaScript-Objekt
    /// </summary>
    procedure LoadFromJSObject(const ADauerdiagnose: TJSObject);
    
    /// <summary>
    /// Konvertiert die Dauerdiagnose zu einem JavaScript-Objekt
    /// </summary>
    function ToJSObject: TJSObject;
    
    /// <summary>
    /// Erstellt einen Anzeigetext für die Dauerdiagnose
    /// </summary>
    function GetDisplayText: string;
    
    /// <summary>
    /// Prüft, ob die Dauerdiagnose gültig ist
    /// </summary>
    function IsValid: Boolean;
    
    /// <summary>
    /// Erstellt eine Kopie der Dauerdiagnose
    /// </summary>
    function Clone: TDauerdiagnosen;
    
    // DTO-spezifische Properties
    /// <summary>
    /// Anzeigetext für die UI
    /// </summary>
    property DisplayText: string read FDisplayText write FDisplayText;
    
    /// <summary>
    /// Gibt an, ob die Dauerdiagnose ausgewählt ist
    /// </summary>
    property IsSelected: Boolean read FIsSelected write FIsSelected;
    
    /// <summary>
    /// Beschreibungstext für den ICD-Code
    /// </summary>
    property IcdCodeText: string read FIcdCodeText write FIcdCodeText;
  end;

  /// <summary>
  /// Liste von Dauerdiagnosen
  /// </summary>
  TDauerdiagnosenList = class
  private
    FItems: TObjectList<TDauerdiagnosen>;
  public
    constructor Create;
    destructor Destroy; override;
    
    /// <summary>
    /// Lädt die Liste aus einem JavaScript-Array
    /// </summary>
    procedure LoadFromJSArray(const JSONArray: TJSArray);
    
    /// <summary>
    /// Konvertiert die Liste zu einem JavaScript-Array
    /// </summary>
    function ToJSArray: TJSArray;
    
    /// <summary>
    /// Fügt eine Dauerdiagnose hinzu
    /// </summary>
    procedure Add(ADauerdiagnose: TDauerdiagnosen);
    
    /// <summary>
    /// Entfernt eine Dauerdiagnose
    /// </summary>
    procedure Remove(ADauerdiagnose: TDauerdiagnosen);
    
    /// <summary>
    /// Leert die Liste
    /// </summary>
    procedure Clear;
    
    /// <summary>
    /// Gibt die Anzahl der Elemente zurück
    /// </summary>
    function Count: Integer;
    
    /// <summary>
    /// Findet eine Dauerdiagnose anhand der Patientennummer und Reihenfolge
    /// </summary>
    function FindByPatnrAndReihenfolge(APatnr: Integer; AReihenfolge: Integer): TDauerdiagnosen;
    
    /// <summary>
    /// Gibt alle ausgewählten Dauerdiagnosen zurück
    /// </summary>
    function GetSelected: TArray<TDauerdiagnosen>;
    
    /// <summary>
    /// Liste der Dauerdiagnosen
    /// </summary>
    property Items: TObjectList<TDauerdiagnosen> read FItems;
  end;

implementation

{ TDauerdiagnosen }

constructor TDauerdiagnosen.Create;
begin
  inherited Create;
  FDisplayText := '';
  FIsSelected := False;
  FIcdCodeText := '';
end;

destructor TDauerdiagnosen.Destroy;
begin
  inherited Destroy;
end;

procedure TDauerdiagnosen.LoadFromJSObject(const ADauerdiagnose: TJSObject);
begin
  if not Assigned(ADauerdiagnose) then
    Exit;

  // Basis-Properties aus der ORM-Klasse
  Patnr := StrToIntDef(string(ADauerdiagnose['Patnr']), 0);
  Quartal := string(ADauerdiagnose['Quartal']);
  Anamnese := string(ADauerdiagnose['Anamnese']);
  Reihenfolge := StrToIntDef(string(ADauerdiagnose['Reihenfolge']), 0);
  
  if ADauerdiagnose['IcdCode'] <> undefined then
    IcdCode := string(ADauerdiagnose['IcdCode']);
    
  if ADauerdiagnose['Diagnosensicherheit'] <> undefined then
    Diagnosensicherheit := string(ADauerdiagnose['Diagnosensicherheit']);
    
  if ADauerdiagnose['Seitenlokalisation'] <> undefined then
    Seitenlokalisation := string(ADauerdiagnose['Seitenlokalisation']);
    
  if ADauerdiagnose['Ausgewaehlt'] <> undefined then
    Ausgewaehlt := string(ADauerdiagnose['Ausgewaehlt']);
    
  if ADauerdiagnose['Leereintrag'] <> undefined then
    Leereintrag := string(ADauerdiagnose['Leereintrag']);

  // DTO-spezifische Properties
  if ADauerdiagnose['DisplayText'] <> undefined then
    FDisplayText := string(ADauerdiagnose['DisplayText']);
    
  if ADauerdiagnose['IsSelected'] <> undefined then
    FIsSelected := Boolean(ADauerdiagnose['IsSelected']);
    
  if ADauerdiagnose['IcdCodeText'] <> undefined then
    FIcdCodeText := string(ADauerdiagnose['IcdCodeText']);
end;

function TDauerdiagnosen.ToJSObject: TJSObject;
begin
  Result := TJSObject.new;
  
  // Basis-Properties
  Result['Patnr'] := Patnr;
  Result['Quartal'] := Quartal;
  Result['Anamnese'] := Anamnese;
  Result['Reihenfolge'] := Reihenfolge;
  
  if IcdCode.HasValue then
    Result['IcdCode'] := IcdCode.Value;
    
  if Diagnosensicherheit.HasValue then
    Result['Diagnosensicherheit'] := Diagnosensicherheit.Value;
    
  if Seitenlokalisation.HasValue then
    Result['Seitenlokalisation'] := Seitenlokalisation.Value;
    
  if Ausgewaehlt.HasValue then
    Result['Ausgewaehlt'] := Ausgewaehlt.Value;
    
  if Leereintrag.HasValue then
    Result['Leereintrag'] := Leereintrag.Value;

  // DTO-spezifische Properties
  Result['DisplayText'] := FDisplayText;
  Result['IsSelected'] := FIsSelected;
  Result['IcdCodeText'] := FIcdCodeText;
end;

function TDauerdiagnosen.GetDisplayText: string;
begin
  if FDisplayText <> '' then
    Result := FDisplayText
  else
  begin
    Result := '';
    if IcdCode.HasValue and (IcdCode.Value <> '') then
      Result := IcdCode.Value;
      
    if FIcdCodeText <> '' then
    begin
      if Result <> '' then
        Result := Result + ' - ' + FIcdCodeText
      else
        Result := FIcdCodeText;
    end;
    
    if Result = '' then
      Result := 'Dauerdiagnose #' + IntToStr(Reihenfolge);
  end;
end;

function TDauerdiagnosen.IsValid: Boolean;
begin
  Result := (Patnr > 0) and (Quartal <> '') and (Anamnese <> '') and (Reihenfolge >= 0);
end;

function TDauerdiagnosen.Clone: TDauerdiagnosen;
begin
  Result := TDauerdiagnosen.Create;
  
  // Basis-Properties kopieren
  Result.Patnr := Self.Patnr;
  Result.Quartal := Self.Quartal;
  Result.Anamnese := Self.Anamnese;
  Result.Reihenfolge := Self.Reihenfolge;
  Result.IcdCode := Self.IcdCode;
  Result.Diagnosensicherheit := Self.Diagnosensicherheit;
  Result.Seitenlokalisation := Self.Seitenlokalisation;
  Result.Ausgewaehlt := Self.Ausgewaehlt;
  Result.Leereintrag := Self.Leereintrag;
  
  // DTO-spezifische Properties kopieren
  Result.FDisplayText := Self.FDisplayText;
  Result.FIsSelected := Self.FIsSelected;
  Result.FIcdCodeText := Self.FIcdCodeText;
end;

{ TDauerdiagnosenList }

constructor TDauerdiagnosenList.Create;
begin
  inherited Create;
  FItems := TObjectList<TDauerdiagnosen>.Create(True);
end;

destructor TDauerdiagnosenList.Destroy;
begin
  FItems.Free;
  inherited Destroy;
end;

procedure TDauerdiagnosenList.LoadFromJSArray(const JSONArray: TJSArray);
var
  i: Integer;
  LDauerdiagnose: TDauerdiagnosen;
begin
  FItems.Clear;
  
  if not Assigned(JSONArray) then
    Exit;
    
  for i := 0 to JSONArray.Length - 1 do
  begin
    LDauerdiagnose := TDauerdiagnosen.Create;
    LDauerdiagnose.LoadFromJSObject(TJSObject(JSONArray[i]));
    FItems.Add(LDauerdiagnose);
  end;
end;

function TDauerdiagnosenList.ToJSArray: TJSArray;
var
  i: Integer;
begin
  Result := TJSArray.new;
  
  for i := 0 to FItems.Count - 1 do
  begin
    Result.push(FItems[i].ToJSObject);
  end;
end;

procedure TDauerdiagnosenList.Add(ADauerdiagnose: TDauerdiagnosen);
begin
  if Assigned(ADauerdiagnose) then
    FItems.Add(ADauerdiagnose);
end;

procedure TDauerdiagnosenList.Remove(ADauerdiagnose: TDauerdiagnosen);
begin
  if Assigned(ADauerdiagnose) then
    FItems.Remove(ADauerdiagnose);
end;

procedure TDauerdiagnosenList.Clear;
begin
  FItems.Clear;
end;

function TDauerdiagnosenList.Count: Integer;
begin
  Result := FItems.Count;
end;

function TDauerdiagnosenList.FindByPatnrAndReihenfolge(APatnr: Integer; AReihenfolge: Integer): TDauerdiagnosen;
var
  i: Integer;
begin
  Result := nil;
  
  for i := 0 to FItems.Count - 1 do
  begin
    if (FItems[i].Patnr = APatnr) and (FItems[i].Reihenfolge = AReihenfolge) then
    begin
      Result := FItems[i];
      Break;
    end;
  end;
end;

function TDauerdiagnosenList.GetSelected: TArray<TDauerdiagnosen>;
var
  i: Integer;
  LSelected: TList<TDauerdiagnosen>;
begin
  LSelected := TList<TDauerdiagnosen>.Create;
  try
    for i := 0 to FItems.Count - 1 do
    begin
      if FItems[i].IsSelected or 
         (FItems[i].Ausgewaehlt.HasValue and (FItems[i].Ausgewaehlt.Value = 'J')) then
        LSelected.Add(FItems[i]);
    end;
    
    Result := LSelected.ToArray;
  finally
    LSelected.Free;
  end;
end;

end.
