﻿unit Stammdaten.Patient;

interface
uses
  System.SysUtils,
  System.Generics.Collections,
  JS, Web;

type
  TPatient = class
  private
    FPatNr: Integer;
    FName: string;
    FVorName: string;
    FGeburtstag: string;
    FStrasse: string;
    FPlz: string;
    FOrt: string;
    FTelefon: string;
    FEMail: string;
    FVersicherung: string;
    FVersicherer: string;
    FBeruf: string;
    FArbeitgeber: string;
    FPatientSeit: string;
    FLetzterBesuch: string;
  public
    procedure LoadFromJSObject(const LPatient: TJSObject);
    property PatNr: Integer read FPatNr write FPatNr;
    property Name: string read FName write FName;
    property VorName: string read FVorName write FVorName;
    property Geburtstag: string read FGeburtstag write FGeburtstag;
    property Strasse: string read FStrasse write FStrasse;
    property Plz: string read FPlz write FPlz;
    property Ort: string read FOrt write FOrt;
    property Telefon: string read FTelefon write FTelefon;
    property EMail: string read FEMail write FEMail;
    property Versicherung: string read FVersicherung write FVersicherung;
    property Versicherer: string read FVersicherer write FVersicherer;
    property Beruf: string read FBeruf write FBeruf;
    property Arbeitgeber: string read FArbeitgeber write FArbeitgeber;
    property PatientSeit: string read FPatientSeit write FPatientSeit;
    property LetzterBesuch: string read FLetzterBesuch write FLetzterBesuch;
  end;

  TPatientList = class
  private
    FItems: TObjectList<TPatient>;
  public
    constructor Create;
    destructor Destroy; override;
    procedure LoadFromJSArray(const JSONArray: TJSArray);
    property Items: TObjectList<TPatient>read FItems;
  end;

  TPatientInfo = class
  private
    FBeh: string;
    FDauerTermin: Integer;
    FNotiz: string;
    FZeitTermin: string;
    FPatient: TPatient;
  public
    constructor Create;
    destructor Destroy; override;

    procedure LoadFromJSObject(const APatientInfo: TJSObject);

    property Beh: string read FBeh write FBeh;
    property DauerTermin: Integer read FDauerTermin write FDauerTermin;
    property Notiz: string read FNotiz write FNotiz;
    property ZeitTermin: string read FZeitTermin write FZeitTermin;
    property Patient: TPatient read FPatient;
  end;

  TPatientInfoList = class
  private
    FItems: TObjectList<TPatientInfo>;
  public
    constructor Create;
    destructor Destroy; override;

    procedure LoadFromJSArray(const JSONArray: TJSArray);

    property Items: TObjectList<TPatientInfo>read FItems;
  end;

implementation

{ TPatientList }
constructor TPatientList.Create;
begin
  inherited Create;
  FItems := TObjectList<TPatient>.Create(True); // True = Objekte werden automatisch freigegeben
end;

destructor TPatientList.Destroy;
begin
  FItems.Free;
  inherited;
end;

procedure TPatientList.LoadFromJSArray(const JSONArray: TJSArray);
var
  I: Integer;
  LPatient: TJSObject;
  Patient: TPatient;
begin
  if not Assigned(JSONArray) then
    Exit;

  for I := 0 to JSONArray.Length - 1 do
  begin
    LPatient := TJSObject(JSONArray[I]);
    Patient := TPatient.Create;
    Patient.LoadFromJSObject(LPatient);
    FItems.Add(Patient);
  end;
end;

{ TPatient.LoadFromJSObject }

procedure TPatient.LoadFromJSObject(const LPatient: TJSObject);
begin
  if Assigned(LPatient) then
  begin
    FPatNr := StrToIntDef(string(LPatient['PatNr']), 0);
    FName := string(LPatient['Name']);
    FVorName := string(LPatient['VorName']);
    FGeburtstag := string(LPatient['Geburtstag']);
    FStrasse := string(LPatient['Strasse']);
    FPlz := string(LPatient['Plz']);
    FOrt := string(LPatient['Ort']);
    FTelefon := string(LPatient['Telefon']);
    FEMail := string(LPatient['EMail']);
    FVersicherung := string(LPatient['Versicherung']);
    FVersicherer := string(LPatient['Versicherer']);
    FBeruf := string(LPatient['Beruf']);
    FArbeitgeber := string(LPatient['Arbeitgeber']);
    FPatientSeit := string(LPatient['PatientSeit']);
    FLetzterBesuch := string(LPatient['LetzterBesuch']);
  end;
end;

{ TPatientInfo }

constructor TPatientInfo.Create;
begin
  inherited Create;
  FPatient := TPatient.Create;
end;

destructor TPatientInfo.Destroy;
begin
  FPatient.Free;
  inherited;
end;

procedure TPatientInfo.LoadFromJSObject(const APatientInfo: TJSObject);
var
  LPatientObj: TJSObject;
begin
  if not Assigned(APatientInfo) then
    Exit;

  FBeh := string(APatientInfo['Beh']);
  FDauerTermin := StrToIntDef(string(APatientInfo['DauerTermin']), 0);
  FNotiz := string(APatientInfo['Notiz']);
  FZeitTermin := string(APatientInfo['ZeitTermin']);

  LPatientObj := TJSObject(APatientInfo['Patient']);
  FPatient.LoadFromJSObject(LPatientObj);
end;

{ TPatientInfoList }

constructor TPatientInfoList.Create;
begin
  inherited Create;
  FItems := TObjectList<TPatientInfo>.Create(True); // True = automatisch freigeben
end;

destructor TPatientInfoList.Destroy;
begin
  FItems.Free;
  inherited;
end;

procedure TPatientInfoList.LoadFromJSArray(const JSONArray: TJSArray);
var
  I: Integer;
  Info: TPatientInfo;
  InfoObj: TJSObject;
begin
  if not Assigned(JSONArray) then
    Exit;

  for I := 0 to JSONArray.Length - 1 do
  begin
    InfoObj := TJSObject(JSONArray[I]);
    Info := TPatientInfo.Create;
    Info.LoadFromJSObject(InfoObj);
    FItems.Add(Info);
  end;
end;
end.

