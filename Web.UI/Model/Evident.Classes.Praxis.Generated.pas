unit Evident.Classes.Praxis.Generated;

interface

uses
  SysUtils, 
  Generics.Collections, 
  Aurelius.Mapping.Attributes, 
  Aurelius.Types.Blob, 
  Aurelius.Types.DynamicProperties, 
  Aurelius.Types.Nullable, 
  Aurelius.Types.Proxy;

type
  TAnam = class;
  TAufgaben = class;
  TDauerdiagnosen = class;
  TIndikat = class;
  TKasse = class;
  TLeistung = class;
  TNotizp = class;
  TPatient = class;
  TPatindik = class;
  TTableBefund = class;
  TZahnbefu = class;
  
  [Entity]
  [Table('ANAM')]
  [Id('FPatnr', TIdGenerator.None)]
  [Model('PRAXIS')]
  [IdUnsavedValue(-1)]
  TAnam = class
  private
    [Column('PATNR', [TColumnProp.Required])]
    FPatnr: Integer;
    
    [Column('INDIKATION', [])]
    FIndikation: Nullable<Integer>;
    
    [Column('ROENTGENDAT', [])]
    FRoentgendat: Nullable<TDateTime>;
    
    [Column('SPRITZEN', [], 40)]
    FSpritzen: Nullable<string>;
    
    [Column('DAUERDIAGNOSEN', [TColumnProp.Lazy], 80)]
    [DBTypeMemo]
    FDauerdiagnosen: TBlob;
    
    [Column('ANZEIGEN', [])]
    FAnzeigen: Nullable<Integer>;
    
    [Column('DATUM', [])]
    FDatum: Nullable<TDateTime>;
    
    [Column('WER', [], 20)]
    FWer: Nullable<string>;
    
    [Column('FTYP', [])]
    FFtyp: Nullable<Integer>;
    
    [Column('FDATUM', [])]
    FFHistDatum: Nullable<TDateTime>;
    
    [Column('AENDVERWEIS', [])]
    FAendverweis: Nullable<Integer>;
    
    [Column('FWER', [], 20)]
    FFHistWer: Nullable<string>;
    
    [Column('AENDWER', [], 20)]
    FAendwer: Nullable<string>;
    
    [Column('AENDDATUM', [])]
    FAenddatum: Nullable<TDateTime>;
    
    [Column('NEUDATUM', [])]
    FNeudatum: Nullable<TDateTime>;
    
    [Column('INFOSKOPANAM', [TColumnProp.Lazy], 80)]
    [DBTypeMemo]
    FInfoskopanam: TBlob;
    
    [Column('FRUEHERKRANK', [TColumnProp.Lazy], 80)]
    [DBTypeMemo]
    FFrueherkrank: TBlob;
  public
    property Patnr: Integer read FPatnr write FPatnr;
    property Indikation: Nullable<Integer> read FIndikation write FIndikation;
    property Roentgendat: Nullable<TDateTime> read FRoentgendat write FRoentgendat;
    property Spritzen: Nullable<string> read FSpritzen write FSpritzen;
    property Dauerdiagnosen: TBlob read FDauerdiagnosen write FDauerdiagnosen;
    property Anzeigen: Nullable<Integer> read FAnzeigen write FAnzeigen;
    property Datum: Nullable<TDateTime> read FDatum write FDatum;
    property Wer: Nullable<string> read FWer write FWer;
    property Ftyp: Nullable<Integer> read FFtyp write FFtyp;
    property FHistDatum: Nullable<TDateTime> read FFHistDatum write FFHistDatum;
    property Aendverweis: Nullable<Integer> read FAendverweis write FAendverweis;
    property FHistWer: Nullable<string> read FFHistWer write FFHistWer;
    property Aendwer: Nullable<string> read FAendwer write FAendwer;
    property Aenddatum: Nullable<TDateTime> read FAenddatum write FAenddatum;
    property Neudatum: Nullable<TDateTime> read FNeudatum write FNeudatum;
    property Infoskopanam: TBlob read FInfoskopanam write FInfoskopanam;
    property Frueherkrank: TBlob read FFrueherkrank write FFrueherkrank;
  end;
  
  [Entity]
  [Table('AUFGABEN')]
  [UniqueKey('ID')]
  [Id('FAufgabennr', TIdGenerator.None)]
  [Id('FPatnr', TIdGenerator.None)]
  [Model('PRAXIS')]
  [IdUnsavedValue(-1)]
  TAufgaben = class
  private
    [Column('ID', [TColumnProp.Required])]
    FId: Int64;
    
    [Column('MODIFIED', [TColumnProp.Required])]
    FModified: TDateTime;
    
    [Column('AUFGABENNR', [TColumnProp.Required])]
    FAufgabennr: Integer;
    
    [Column('PATNR', [TColumnProp.Required])]
    FPatnr: Integer;
    
    [Column('AUFGABE_AM', [])]
    FAufgabeAm: Nullable<TDateTime>;
    
    [Column('NOTIZ', [TColumnProp.Lazy], 80)]
    [DBTypeMemo]
    FNotiz: TBlob;
    
    [Column('INTERVALL', [])]
    FIntervall: Nullable<Integer>;
    
    [Column('ANZEINHEIT', [])]
    FAnzeinheit: Nullable<Integer>;
    
    [Column('ARTEINHEIT', [])]
    FArteinheit: Nullable<Integer>;
    
    [Column('ANZAKT', [])]
    FAnzakt: Nullable<Integer>;
    
    [Column('INTERVALLNR', [])]
    FIntervallnr: Nullable<Integer>;
    
    [Column('BEH', [], 3)]
    FBeh: Nullable<string>;
    
    [Column('ERLEDIGT', [], 1)]
    FErledigt: Nullable<string>;
    
    [Column('ERLEDIGT_VON', [], 16)]
    FErledigtVon: Nullable<string>;
    
    [Column('ERLEDIGT_AM', [])]
    FErledigtAm: Nullable<TDateTime>;
    
    [Column('ENTFERNT', [], 1)]
    FEntfernt: Nullable<string>;
    
    [Column('ENTFERNT_AM', [])]
    FEntferntAm: Nullable<TDateTime>;
  public
    property Id: Int64 read FId write FId;
    property Modified: TDateTime read FModified write FModified;
    property Aufgabennr: Integer read FAufgabennr write FAufgabennr;
    property Patnr: Integer read FPatnr write FPatnr;
    property AufgabeAm: Nullable<TDateTime> read FAufgabeAm write FAufgabeAm;
    property Notiz: TBlob read FNotiz write FNotiz;
    property Intervall: Nullable<Integer> read FIntervall write FIntervall;
    property Anzeinheit: Nullable<Integer> read FAnzeinheit write FAnzeinheit;
    property Arteinheit: Nullable<Integer> read FArteinheit write FArteinheit;
    property Anzakt: Nullable<Integer> read FAnzakt write FAnzakt;
    property Intervallnr: Nullable<Integer> read FIntervallnr write FIntervallnr;
    property Beh: Nullable<string> read FBeh write FBeh;
    property Erledigt: Nullable<string> read FErledigt write FErledigt;
    property ErledigtVon: Nullable<string> read FErledigtVon write FErledigtVon;
    property ErledigtAm: Nullable<TDateTime> read FErledigtAm write FErledigtAm;
    property Entfernt: Nullable<string> read FEntfernt write FEntfernt;
    property EntferntAm: Nullable<TDateTime> read FEntferntAm write FEntferntAm;
  end;
  
  [Entity]
  [Table('DAUERDIAGNOSEN')]
  [UniqueKey('PATNR, QUARTAL, ANAMNESE, AUSGEWAEHLT, REIHENFOLGE')]
  [Id('FPatnr', TIdGenerator.None)]
  [Id('FQuartal', TIdGenerator.None)]
  [Id('FAnamnese', TIdGenerator.None)]
  [Id('FReihenfolge', TIdGenerator.None)]
  [Model('PRAXIS')]
  [IdUnsavedValue(-1)]
  TDauerdiagnosen = class
  private
    [Column('PATNR', [TColumnProp.Required])]
    FPatnr: Integer;
    
    [Column('QUARTAL', [TColumnProp.Required], 5)]
    FQuartal: string;
    
    [Column('ANAMNESE', [TColumnProp.Required], 1)]
    FAnamnese: string;
    
    [Column('REIHENFOLGE', [TColumnProp.Required])]
    FReihenfolge: Integer;
    
    [Column('ICD_CODE', [], 6)]
    FIcdCode: Nullable<string>;
    
    [Column('DIAGNOSENSICHERHEIT', [], 1)]
    FDiagnosensicherheit: Nullable<string>;
    
    [Column('SEITENLOKALISATION', [], 1)]
    FSeitenlokalisation: Nullable<string>;
    
    [Column('DIAGNOSENERLAEUTERUNG', [TColumnProp.Lazy], 80)]
    [DBTypeMemo]
    FDiagnosenerlaeuterung: TBlob;
    
    [Column('DIAGNOSEAUSNAHME', [TColumnProp.Lazy], 80)]
    [DBTypeMemo]
    FDiagnoseausnahme: TBlob;
    
    [Column('AUSGEWAEHLT', [], 1)]
    FAusgewaehlt: Nullable<string>;
    
    [Column('LEEREINTRAG', [], 1)]
    FLeereintrag: Nullable<string>;
  public
    property Patnr: Integer read FPatnr write FPatnr;
    property Quartal: string read FQuartal write FQuartal;
    property Anamnese: string read FAnamnese write FAnamnese;
    property Reihenfolge: Integer read FReihenfolge write FReihenfolge;
    property IcdCode: Nullable<string> read FIcdCode write FIcdCode;
    property Diagnosensicherheit: Nullable<string> read FDiagnosensicherheit write FDiagnosensicherheit;
    property Seitenlokalisation: Nullable<string> read FSeitenlokalisation write FSeitenlokalisation;
    property Diagnosenerlaeuterung: TBlob read FDiagnosenerlaeuterung write FDiagnosenerlaeuterung;
    property Diagnoseausnahme: TBlob read FDiagnoseausnahme write FDiagnoseausnahme;
    property Ausgewaehlt: Nullable<string> read FAusgewaehlt write FAusgewaehlt;
    property Leereintrag: Nullable<string> read FLeereintrag write FLeereintrag;
  end;
  
  [Entity]
  [Table('INDIKAT')]
  [Id('FIndinr', TIdGenerator.None)]
  [Model('PRAXIS')]
  [IdUnsavedValue(-1)]
  TIndikat = class
  private
    [Column('INDINR', [TColumnProp.Required])]
    FIndinr: Integer;
    
    [Column('TYP', [TColumnProp.Required])]
    FTyp: Integer;
    
    [Column('BEZEICHNUNG', [TColumnProp.Required], 50)]
    FBezeichnung: string;
  public
    property Indinr: Integer read FIndinr write FIndinr;
    property Typ: Integer read FTyp write FTyp;
    property Bezeichnung: string read FBezeichnung write FBezeichnung;
  end;
  
  [Entity]
  [Table('KASSE')]
  [Id('FKasseId', TIdGenerator.None)]
  [Model('PRAXIS')]
  [IdUnsavedValue(-1)]
  TKasse = class
  private
    [Column('KASSE_ID', [TColumnProp.Required])]
    FKasseId: Integer;
    
    [Column('KASSKUERZEL', [], 18)]
    FKasskuerzel: Nullable<string>;
    
    [Column('KURZBEZ', [], 30)]
    FKurzbez: Nullable<string>;
    
    [Column('KIKNR', [], 7)]
    FKiknr: Nullable<string>;
    
    [Column('VKNR', [], 5)]
    FVknr: Nullable<string>;
    
    [Column('VKUNTERNR', [], 2)]
    FVkunternr: Nullable<string>;
    
    [Column('KZVNR', [], 12)]
    FKzvnr: Nullable<string>;
    
    [Column('KASSART', [])]
    FKassart: Nullable<Integer>;
    
    [Column('ERWKASSART', [])]
    FErwkassart: Nullable<Integer>;
    
    [Column('PROTHZUSCHUSS', [])]
    FProthzuschuss: Nullable<Integer>;
    
    [Column('STATABZUG', [])]
    FStatabzug: Nullable<Integer>;
    
    [Column('GOZ', [])]
    FGoz: Nullable<Integer>;
    
    [Column('GOZ_PKTW', [])]
    FGozPktw: Nullable<Double>;
    
    [Column('GOZ_PKTW_M', [])]
    FGozPktwM: Nullable<Double>;
    
    [Column('GOZ_PKTW_SONST', [])]
    FGozPktwSonst: Nullable<Double>;
    
    [Column('GOAE_KTLG100', [])]
    FGoaeKtlg100: Nullable<Double>;
    
    [Column('GOAE_KTLG101', [])]
    FGoaeKtlg101: Nullable<Double>;
    
    [Column('GOAE_KTLG102', [])]
    FGoaeKtlg102: Nullable<Double>;
    
    [Column('PKTW_HEIL1', [])]
    FPktwHeil1: Nullable<Double>;
    
    [Column('PKTW_HEIL2', [])]
    FPktwHeil2: Nullable<Double>;
    
    [Column('PKTW_REGEL_M', [])]
    FPktwRegelM: Nullable<Double>;
    
    [Column('PKTW_REGEL_AP', [])]
    FPktwRegelAp: Nullable<Double>;
    
    [Column('INTERN', [])]
    FIntern: Nullable<Integer>;
    
    [Column('KASSBEZ', [], 30)]
    FKassbez: Nullable<string>;
    
    [Column('FREMD', [])]
    FFremd: Nullable<Integer>;
    
    [Column('GEBORD', [], 60)]
    FGebord: Nullable<string>;
    
    [Column('LABOR', [])]
    FLabor: Nullable<Integer>;
    
    [Column('LETZTESAQ', [], 5)]
    FLetztesaq: Nullable<string>;
    
    [Column('PKTW_KC_D', [])]
    FPktwKcD: Nullable<Double>;
    
    [Column('PKTW_PA_D', [])]
    FPktwPaD: Nullable<Double>;
    
    [Column('PKTW_KB_D', [])]
    FPktwKbD: Nullable<Double>;
    
    [Column('PKTW_ZE_D', [])]
    FPktwZeD: Nullable<Double>;
    
    [Column('PKTW_KF_D', [])]
    FPktwKfD: Nullable<Double>;
    
    [Column('PKTW_IPALT_D', [])]
    FPktwIpaltD: Nullable<Double>;
    
    [Column('PKTW_IP_D', [])]
    FPktwIpD: Nullable<Double>;
    
    [Column('AE_PKTW_PRAEV_D', [])]
    FAePktwPraevD: Nullable<Double>;
    
    [Column('AE_PKTW_OP_D', [])]
    FAePktwOpD: Nullable<Double>;
    
    [Column('AE_PKTW_LABOR_D', [])]
    FAePktwLaborD: Nullable<Double>;
    
    [Column('AE_PKTW_SONST_D', [])]
    FAePktwSonstD: Nullable<Double>;
    
    [Column('PKTW_KC_E', [])]
    FPktwKcE: Nullable<Double>;
    
    [Column('PKTW_PA_E', [])]
    FPktwPaE: Nullable<Double>;
    
    [Column('PKTW_KB_E', [])]
    FPktwKbE: Nullable<Double>;
    
    [Column('PKTW_ZE_E', [])]
    FPktwZeE: Nullable<Double>;
    
    [Column('PKTW_KF_E', [])]
    FPktwKfE: Nullable<Double>;
    
    [Column('PKTW_IPALT_E', [])]
    FPktwIpaltE: Nullable<Double>;
    
    [Column('PKTW_IP_E', [])]
    FPktwIpE: Nullable<Double>;
    
    [Column('AE_PKTW_PRAEV_E', [])]
    FAePktwPraevE: Nullable<Double>;
    
    [Column('AE_PKTW_OP_E', [])]
    FAePktwOpE: Nullable<Double>;
    
    [Column('AE_PKTW_LABOR_E', [])]
    FAePktwLaborE: Nullable<Double>;
    
    [Column('AE_PKTW_SONST_E', [])]
    FAePktwSonstE: Nullable<Double>;
    
    [Column('MEDIZINAL', [])]
    FMedizinal: Nullable<Integer>;
    
    [Column('KEIN_PG_AUTO', [], 1)]
    FKeinPgAuto: Nullable<string>;
    
    [Column('KFOPAUSCHAL', [], 1)]
    FKfopauschal: Nullable<string>;
    
    [Column('KEIN_HAERTEFALLKZ', [], 1)]
    FKeinHaertefallkz: Nullable<string>;
    
    [Column('KTKENNUNG', [], 9)]
    FKtkennung: Nullable<string>;
    
    [Column('GENEHMIGUNG_HKP_FZ51', [], 1)]
    FGenehmigungHkpFz51: Nullable<string>;
    
    [Column('GENEHMIGUNG_HKP_OPTIONAL', [], 1)]
    FGenehmigungHkpOptional: Nullable<string>;
    
    [Column('SYNCSTATE', [TColumnProp.Required])]
    FSyncstate: Integer;
    
    [Column('PROTHZUSSYSTEM', [])]
    FProthzussystem: Nullable<Integer>;
    
    [Column('KEIN_EBZ', [], 1)]
    FKeinEbz: Nullable<string>;
  public
    property KasseId: Integer read FKasseId write FKasseId;
    property Kasskuerzel: Nullable<string> read FKasskuerzel write FKasskuerzel;
    property Kurzbez: Nullable<string> read FKurzbez write FKurzbez;
    property Kiknr: Nullable<string> read FKiknr write FKiknr;
    property Vknr: Nullable<string> read FVknr write FVknr;
    property Vkunternr: Nullable<string> read FVkunternr write FVkunternr;
    property Kzvnr: Nullable<string> read FKzvnr write FKzvnr;
    property Kassart: Nullable<Integer> read FKassart write FKassart;
    property Erwkassart: Nullable<Integer> read FErwkassart write FErwkassart;
    property Prothzuschuss: Nullable<Integer> read FProthzuschuss write FProthzuschuss;
    property Statabzug: Nullable<Integer> read FStatabzug write FStatabzug;
    property Goz: Nullable<Integer> read FGoz write FGoz;
    property GozPktw: Nullable<Double> read FGozPktw write FGozPktw;
    property GozPktwM: Nullable<Double> read FGozPktwM write FGozPktwM;
    property GozPktwSonst: Nullable<Double> read FGozPktwSonst write FGozPktwSonst;
    property GoaeKtlg100: Nullable<Double> read FGoaeKtlg100 write FGoaeKtlg100;
    property GoaeKtlg101: Nullable<Double> read FGoaeKtlg101 write FGoaeKtlg101;
    property GoaeKtlg102: Nullable<Double> read FGoaeKtlg102 write FGoaeKtlg102;
    property PktwHeil1: Nullable<Double> read FPktwHeil1 write FPktwHeil1;
    property PktwHeil2: Nullable<Double> read FPktwHeil2 write FPktwHeil2;
    property PktwRegelM: Nullable<Double> read FPktwRegelM write FPktwRegelM;
    property PktwRegelAp: Nullable<Double> read FPktwRegelAp write FPktwRegelAp;
    property Intern: Nullable<Integer> read FIntern write FIntern;
    property Kassbez: Nullable<string> read FKassbez write FKassbez;
    property Fremd: Nullable<Integer> read FFremd write FFremd;
    property Gebord: Nullable<string> read FGebord write FGebord;
    property Labor: Nullable<Integer> read FLabor write FLabor;
    property Letztesaq: Nullable<string> read FLetztesaq write FLetztesaq;
    property PktwKcD: Nullable<Double> read FPktwKcD write FPktwKcD;
    property PktwPaD: Nullable<Double> read FPktwPaD write FPktwPaD;
    property PktwKbD: Nullable<Double> read FPktwKbD write FPktwKbD;
    property PktwZeD: Nullable<Double> read FPktwZeD write FPktwZeD;
    property PktwKfD: Nullable<Double> read FPktwKfD write FPktwKfD;
    property PktwIpaltD: Nullable<Double> read FPktwIpaltD write FPktwIpaltD;
    property PktwIpD: Nullable<Double> read FPktwIpD write FPktwIpD;
    property AePktwPraevD: Nullable<Double> read FAePktwPraevD write FAePktwPraevD;
    property AePktwOpD: Nullable<Double> read FAePktwOpD write FAePktwOpD;
    property AePktwLaborD: Nullable<Double> read FAePktwLaborD write FAePktwLaborD;
    property AePktwSonstD: Nullable<Double> read FAePktwSonstD write FAePktwSonstD;
    property PktwKcE: Nullable<Double> read FPktwKcE write FPktwKcE;
    property PktwPaE: Nullable<Double> read FPktwPaE write FPktwPaE;
    property PktwKbE: Nullable<Double> read FPktwKbE write FPktwKbE;
    property PktwZeE: Nullable<Double> read FPktwZeE write FPktwZeE;
    property PktwKfE: Nullable<Double> read FPktwKfE write FPktwKfE;
    property PktwIpaltE: Nullable<Double> read FPktwIpaltE write FPktwIpaltE;
    property PktwIpE: Nullable<Double> read FPktwIpE write FPktwIpE;
    property AePktwPraevE: Nullable<Double> read FAePktwPraevE write FAePktwPraevE;
    property AePktwOpE: Nullable<Double> read FAePktwOpE write FAePktwOpE;
    property AePktwLaborE: Nullable<Double> read FAePktwLaborE write FAePktwLaborE;
    property AePktwSonstE: Nullable<Double> read FAePktwSonstE write FAePktwSonstE;
    property Medizinal: Nullable<Integer> read FMedizinal write FMedizinal;
    property KeinPgAuto: Nullable<string> read FKeinPgAuto write FKeinPgAuto;
    property Kfopauschal: Nullable<string> read FKfopauschal write FKfopauschal;
    property KeinHaertefallkz: Nullable<string> read FKeinHaertefallkz write FKeinHaertefallkz;
    property Ktkennung: Nullable<string> read FKtkennung write FKtkennung;
    property GenehmigungHkpFz51: Nullable<string> read FGenehmigungHkpFz51 write FGenehmigungHkpFz51;
    property GenehmigungHkpOptional: Nullable<string> read FGenehmigungHkpOptional write FGenehmigungHkpOptional;
    property Syncstate: Integer read FSyncstate write FSyncstate;
    property Prothzussystem: Nullable<Integer> read FProthzussystem write FProthzussystem;
    property KeinEbz: Nullable<string> read FKeinEbz write FKeinEbz;
  end;
  
  [Entity]
  [Table('LEISTUNG')]
  [UniqueKey('ETI_ID')]
  [Id('FPatnr', TIdGenerator.None)]
  [Id('FLfdnr', TIdGenerator.None)]
  [Model('PRAXIS')]
  [IdUnsavedValue(-1)]
  TLeistung = class
  private
    [Column('PATNR', [TColumnProp.Required])]
    FPatnr: Integer;
    
    [Column('LFDNR', [TColumnProp.Required])]
    FLfdnr: Integer;
    
    [Column('DATUM', [TColumnProp.Required])]
    FDatum: TDateTime;
    
    [Column('SITZKZ', [TColumnProp.Required])]
    FSitzkz: Integer;
    
    [Column('POS', [TColumnProp.Required])]
    FPos: Integer;
    
    [Column('ZAEHNEOK', [])]
    FZaehneok: Nullable<Integer>;
    
    [Column('ZAEHNEUK', [])]
    FZaehneuk: Nullable<Integer>;
    
    [Column('ABRECHNEN', [])]
    FAbrechnen: Nullable<Integer>;
    
    [Column('GEBNR', [], 10)]
    FGebnr: Nullable<string>;
    
    [Column('SCHLGEBNR', [], 10)]
    FSchlgebnr: Nullable<string>;
    
    [Column('GEBORDNUNG', [TColumnProp.Required])]
    FGebordnung: Integer;
    
    [Column('ANZAHL', [])]
    FAnzahl: Nullable<Integer>;
    
    [Column('FUELLUNG', [])]
    FFuellung: Nullable<Integer>;
    
    [Column('FUELLMAT', [])]
    FFuellmat: Nullable<Integer>;
    
    [Column('BEHANDLER', [], 3)]
    FBehandler: Nullable<string>;
    
    [Column('LSTART', [TColumnProp.Required])]
    FLstart: Integer;
    
    [Column('SCHEINART', [])]
    FScheinart: Nullable<Integer>;
    
    [Column('MATSACHKOS', [])]
    FMatsachkos: Nullable<Double>;
    
    [Column('ABRDAT', [])]
    FAbrdat: Nullable<TDateTime>;
    
    [Column('BEGR', [TColumnProp.Lazy], 80)]
    [DBTypeMemo]
    FBegr: TBlob;
    
    [Column('FAKTOR', [])]
    FFaktor: Nullable<Double>;
    
    [Column('NFAKTOR', [])]
    FNfaktor: Nullable<Double>;
    
    [Column('PGLOB', [])]
    FPglob: Nullable<Integer>;
    
    [Column('PBEGR', [TColumnProp.Lazy], 80)]
    [DBTypeMemo]
    FPbegr: TBlob;
    
    [Column('EINGDAT', [])]
    FEingdat: Nullable<TDateTime>;
    
    [Column('AENDDAT', [])]
    FAenddat: Nullable<TDateTime>;
    
    [Column('PKTWKASSE', [])]
    FPktwkasse: Nullable<Double>;
    
    [Column('PKTWGEBO', [])]
    FPktwgebo: Nullable<Double>;
    
    [Column('DMWERT', [])]
    FDmwert: Nullable<Double>;
    
    [Column('LABVERW', [])]
    FLabverw: Nullable<Integer>;
    
    [Column('ABRQUARTAL', [], 5)]
    FAbrquartal: Nullable<string>;
    
    [Column('VERWEISTYP', [])]
    FVerweistyp: Nullable<Integer>;
    
    [Column('VERWEIS', [])]
    FVerweis: Nullable<Integer>;
    
    [Column('PRIVAT', [])]
    FPrivat: Nullable<Integer>;
    
    [Column('UHRZEIT', [])]
    FUhrzeit: Nullable<TDateTime>;
    
    [Column('PROZENT', [])]
    FProzent: Nullable<Integer>;
    
    [Column('WUSER', [])]
    FWuser: Nullable<Integer>;
    
    [Column('LUSER', [])]
    FLuser: Nullable<Integer>;
    
    [Column('WEITERVA', [])]
    FWeiterva: Nullable<Integer>;
    
    [Column('PLANNR', [])]
    FPlannr: Nullable<Integer>;
    
    [Column('MULTIMEDIANR', [])]
    FMultimedianr: Nullable<Integer>;
    
    [Column('MWSTTYP', [])]
    FMwsttyp: Nullable<Integer>;
    
    [Column('MWSTSATZ', [])]
    FMwstsatz: Nullable<Double>;
    
    [Column('BEGRAUFRECH', [], 1)]
    FBegraufrech: Nullable<string>;
    
    [Column('WEITERVA2', [])]
    FWeiterva2: Nullable<Integer>;
  public
    property Patnr: Integer read FPatnr write FPatnr;
    property Lfdnr: Integer read FLfdnr write FLfdnr;
    property Datum: TDateTime read FDatum write FDatum;
    property Sitzkz: Integer read FSitzkz write FSitzkz;
    property Pos: Integer read FPos write FPos;
    property Zaehneok: Nullable<Integer> read FZaehneok write FZaehneok;
    property Zaehneuk: Nullable<Integer> read FZaehneuk write FZaehneuk;
    property Abrechnen: Nullable<Integer> read FAbrechnen write FAbrechnen;
    property Gebnr: Nullable<string> read FGebnr write FGebnr;
    property Schlgebnr: Nullable<string> read FSchlgebnr write FSchlgebnr;
    property Gebordnung: Integer read FGebordnung write FGebordnung;
    property Anzahl: Nullable<Integer> read FAnzahl write FAnzahl;
    property Fuellung: Nullable<Integer> read FFuellung write FFuellung;
    property Fuellmat: Nullable<Integer> read FFuellmat write FFuellmat;
    property Behandler: Nullable<string> read FBehandler write FBehandler;
    property Lstart: Integer read FLstart write FLstart;
    property Scheinart: Nullable<Integer> read FScheinart write FScheinart;
    property Matsachkos: Nullable<Double> read FMatsachkos write FMatsachkos;
    property Abrdat: Nullable<TDateTime> read FAbrdat write FAbrdat;
    property Begr: TBlob read FBegr write FBegr;
    property Faktor: Nullable<Double> read FFaktor write FFaktor;
    property Nfaktor: Nullable<Double> read FNfaktor write FNfaktor;
    property Pglob: Nullable<Integer> read FPglob write FPglob;
    property Pbegr: TBlob read FPbegr write FPbegr;
    property Eingdat: Nullable<TDateTime> read FEingdat write FEingdat;
    property Aenddat: Nullable<TDateTime> read FAenddat write FAenddat;
    property Pktwkasse: Nullable<Double> read FPktwkasse write FPktwkasse;
    property Pktwgebo: Nullable<Double> read FPktwgebo write FPktwgebo;
    property Dmwert: Nullable<Double> read FDmwert write FDmwert;
    property Labverw: Nullable<Integer> read FLabverw write FLabverw;
    property Abrquartal: Nullable<string> read FAbrquartal write FAbrquartal;
    property Verweistyp: Nullable<Integer> read FVerweistyp write FVerweistyp;
    property Verweis: Nullable<Integer> read FVerweis write FVerweis;
    property Privat: Nullable<Integer> read FPrivat write FPrivat;
    property Uhrzeit: Nullable<TDateTime> read FUhrzeit write FUhrzeit;
    property Prozent: Nullable<Integer> read FProzent write FProzent;
    property Wuser: Nullable<Integer> read FWuser write FWuser;
    property Luser: Nullable<Integer> read FLuser write FLuser;
    property Weiterva: Nullable<Integer> read FWeiterva write FWeiterva;
    property Plannr: Nullable<Integer> read FPlannr write FPlannr;
    property Multimedianr: Nullable<Integer> read FMultimedianr write FMultimedianr;
    property Mwsttyp: Nullable<Integer> read FMwsttyp write FMwsttyp;
    property Mwstsatz: Nullable<Double> read FMwstsatz write FMwstsatz;
    property Begraufrech: Nullable<string> read FBegraufrech write FBegraufrech;
    property Weiterva2: Nullable<Integer> read FWeiterva2 write FWeiterva2;
  end;
  
  [Entity]
  [Table('NOTIZP')]
  [Id('FPatnr', TIdGenerator.None)]
  [Id('FLfdnr', TIdGenerator.None)]
  [Model('PRAXIS')]
  [IdUnsavedValue(-1)]
  TNotizp = class
  private
    [Column('PATNR', [TColumnProp.Required])]
    FPatnr: Integer;
    
    [Column('LFDNR', [TColumnProp.Required])]
    FLfdnr: Integer;
    
    [Column('NOTIZ', [], 0)]
    [DBTypeMemo]
    FNotiz: Nullable<String>;
    
    [Column('ERSTELLT_AM', [])]
    FErstelltAm: Nullable<TDateTime>;
    
    [Column('UPDATE_AM', [])]
    FUpdateAm: Nullable<TDateTime>;
    
    [Column('PRIORITEAT', [])]
    FPrioriteat: Nullable<Integer>;
    
    [Column('SHOWNOTIZ', [], 1)]
    FShownotiz: Nullable<string>;
  public
    property Patnr: Integer read FPatnr write FPatnr;
    property Lfdnr: Integer read FLfdnr write FLfdnr;
    property Notiz: Nullable<String> read FNotiz write FNotiz;
    property ErstelltAm: Nullable<TDateTime> read FErstelltAm write FErstelltAm;
    property UpdateAm: Nullable<TDateTime> read FUpdateAm write FUpdateAm;
    property Prioriteat: Nullable<Integer> read FPrioriteat write FPrioriteat;
    property Shownotiz: Nullable<string> read FShownotiz write FShownotiz;
  end;
  
  [Entity]
  [Table('PATIENT')]
  [UniqueKey('ETI_ID')]
  [Sequence('DATABASE_IDENT')]
  [Id('FPatnr', TIdGenerator.IdentityOrSequence)]
  [Model('PRAXIS')]
  [IdUnsavedValue(-1)]
  TPatient = class
  private
    [Column('PATNR', [TColumnProp.Required])]
    FPatnr: Integer;
    
    [Column('KASSE_ID', [])]
    FKasseId: Nullable<Integer>;
    
    [Column('KASSEKZ', [], 2)]
    FKassekz: Nullable<string>;
    
    [Column('ARBGEB', [], 40)]
    FArbgeb: Nullable<string>;
    
    [Column('TELARBGEB', [], 20)]
    FTelarbgeb: Nullable<string>;
    
    [Column('VERSADR', [])]
    FVersadr: Nullable<Integer>;
    
    [Column('ABWADR', [])]
    FAbwadr: Nullable<Integer>;
    
    [Column('HAUSARZTADR', [])]
    FHausarztadr: Nullable<Integer>;
    
    [Column('UEBERWEISERADR', [])]
    FUeberweiseradr: Nullable<Integer>;
    
    [Column('NACHRICHTL1ADR', [])]
    FNachrichtl1adr: Nullable<Integer>;
    
    [Column('NACHRICHTL2ADR', [])]
    FNachrichtl2adr: Nullable<Integer>;
    
    [Column('MAHNEN', [])]
    FMahnen: Nullable<Integer>;
    
    [Column('BANKEINZUG', [])]
    FBankeinzug: Nullable<Integer>;
    
    [Column('PROTHZUSCHUSS', [])]
    FProthzuschuss: Nullable<Integer>;
    
    [Column('ANAMNESE', [])]
    FAnamnese: Nullable<Integer>;
    
    [Column('EINLDAT1', [])]
    FEinldat1: Nullable<TDateTime>;
    
    [Column('EINLDAT2', [])]
    FEinldat2: Nullable<TDateTime>;
    
    [Column('KFOZUSCHUSS', [])]
    FKfozuschuss: Nullable<Integer>;
    
    [Column('FREIW_VERS', [])]
    FFreiwVers: Nullable<Integer>;
    
    [Column('VERSNR', [], 70)]
    FVersnr: Nullable<string>;
    
    [Column('KENNZ', [])]
    FKennz: Nullable<Integer>;
    
    [Column('GLOB_PATNR', [])]
    FGlobPatnr: Nullable<Integer>;
    
    [Column('GEBDAT', [])]
    FGebdat: Nullable<TDateTime>;
    
    [Column('GESCHLECHT', [], 1)]
    FGeschlecht: Nullable<string>;
    
    [Column('TITEL', [], 30)]
    FTitel: Nullable<string>;
    
    [Column('ANREDE', [], 30)]
    FAnrede: Nullable<string>;
    
    [Column('BRIEFANREDE', [], 100)]
    FBriefanrede: Nullable<string>;
    
    [Column('NAMENSZUSATZ', [], 41)]
    FNamenszusatz: Nullable<string>;
    
    [Column('NACHNAME', [], 45)]
    FNachname: Nullable<string>;
    
    [Column('VORNAME', [], 45)]
    FVorname: Nullable<string>;
    
    [Column('SOUNDEXNACHNAME', [], 45)]
    FSoundexnachname: Nullable<string>;
    
    [Column('SOUNDEXVORNAME', [], 45)]
    FSoundexvorname: Nullable<string>;
    
    [Column('STRASSE', [], 56)]
    FStrasse: Nullable<string>;
    
    [Column('LKZ', [], 3)]
    FLkz: Nullable<string>;
    
    [Column('PLZ', [], 10)]
    FPlz: Nullable<string>;
    
    [Column('ORT', [], 40)]
    FOrt: Nullable<string>;
    
    [Column('POSTFACH', [], 12)]
    FPostfach: Nullable<string>;
    
    [Column('PSF_PLZ', [], 10)]
    FPsfPlz: Nullable<string>;
    
    [Column('PSF_ORT', [], 40)]
    FPsfOrt: Nullable<string>;
    
    [Column('TELEFON1', [], 20)]
    FTelefon1: Nullable<string>;
    
    [Column('TELEFON2', [], 20)]
    FTelefon2: Nullable<string>;
    
    [Column('FAX', [], 20)]
    FFax: Nullable<string>;
    
    [Column('EMAIL', [], 60)]
    FEmail: Nullable<string>;
    
    [Column('BERUF', [], 30)]
    FBeruf: Nullable<string>;
    
    [Column('DUZFREUND', [], 1)]
    FDuzfreund: Nullable<string>;
    
    [Column('ANZAHLUNG', [])]
    FAnzahlung: Nullable<Double>;
    
    [Column('STAMMBEHANDLER', [], 3)]
    FStammbehandler: Nullable<string>;
    
    [Column('AUFNAHMEDATUM', [])]
    FAufnahmedatum: Nullable<TDateTime>;
    
    [Column('RECHBANKKONTO', [])]
    FRechbankkonto: Nullable<Integer>;
    
    [Column('ARCHIVIERT', [], 1)]
    FArchiviert: Nullable<string>;
    
    [Column('ARCHIVGESPERRT', [], 1)]
    FArchivgesperrt: Nullable<string>;
    
    [Column('UEBERNAHME', [], 1)]
    FUebernahme: Nullable<string>;
    
    [Column('ARCHIVIERTAM', [])]
    FArchiviertam: Nullable<TDateTime>;
    
    [Column('ARCHIVIERTBENUTZER', [], 100)]
    FArchiviertbenutzer: Nullable<string>;
    
    [Column('KONTAKTART', [])]
    FKontaktart: Nullable<Integer>;
    
    [Column('VERSTORBEN', [], 1)]
    FVerstorben: Nullable<string>;
    
    [Column('SMS1', [], 20)]
    FSms1: Nullable<string>;
    
    [Column('GEBORT', [], 30)]
    FGebort: Nullable<string>;
    
    [Column('GEBNAME', [], 30)]
    FGebname: Nullable<string>;
    
    [Column('VDDSRZ_DEFAULT', [], 1)]
    FVddsrzDefault: Nullable<string>;
    
    [Column('PRENOTIFICATION', [])]
    FPrenotification: Nullable<Integer>;
    
    [Column('SPERRE_TIS', [], 1)]
    FSperreTis: Nullable<string>;
    
    [Column('SPERRE_EMAIL', [], 1)]
    FSperreEmail: Nullable<string>;
    
    [Column('SPERRE_SMS', [], 1)]
    FSperreSms: Nullable<string>;
    
    [Column('SPERRE_PATINFO', [], 1)]
    FSperrePatinfo: Nullable<string>;
    
    [Column('HAT_BEIHILFE', [], 1)]
    FHatBeihilfe: Nullable<string>;
    
    [Column('ZUZAHLUNGSBEFREIT', [], 1)]
    FZuzahlungsbefreit: Nullable<string>;
    
    [Column('ZUZAHLUNGSBEFREIT_DATUM', [])]
    FZuzahlungsbefreitDatum: Nullable<TDateTime>;
    
    [Column('NAMENSZUSATZ_EGK', [], 30)]
    FNamenszusatzEgk: Nullable<string>;
    
    [Column('VORSATZWORT', [], 20)]
    FVorsatzwort: Nullable<string>;
    
    [Column('STRASSE_EGK', [], 46)]
    FStrasseEgk: Nullable<string>;
    
    [Column('HAUSNUMMER', [], 9)]
    FHausnummer: Nullable<string>;
    
    [Column('ANSCHRIFTENZUSATZ', [], 40)]
    FAnschriftenzusatz: Nullable<string>;
    
    [Column('LKZ_EGK', [], 3)]
    FLkzEgk: Nullable<string>;
    
    [Column('PLZ_EGK', [], 10)]
    FPlzEgk: Nullable<string>;
    
    [Column('ORT_EGK', [], 40)]
    FOrtEgk: Nullable<string>;
    
    [Column('PSF_LKZ', [], 3)]
    FPsfLkz: Nullable<string>;
    
    [Column('INFEKTIONSRISIKO', [], 1)]
    FInfektionsrisiko: Nullable<string>;
    
    [Column('TIS_REMIND', [], 1)]
    FTisRemind: Nullable<string>;
    
    [Column('TIS_REMIND_SMS', [], 1)]
    FTisRemindSms: Nullable<string>;
    
    [Column('TIS_REMIND_EMAIL', [], 1)]
    FTisRemindEmail: Nullable<string>;
    
    [Column('TIS_REMIND_SMS_VERS', [], 1)]
    FTisRemindSmsVers: Nullable<string>;
    
    [Column('TIS_REMIND_EMAIL_VERS', [], 1)]
    FTisRemindEmailVers: Nullable<string>;
    
    [Column('TIS_REMIND_SMS_RECH', [], 1)]
    FTisRemindSmsRech: Nullable<string>;
    
    [Column('TIS_REMIND_EMAIL_RECH', [], 1)]
    FTisRemindEmailRech: Nullable<string>;
    
    [Column('TIS_REMIND_TYP', [])]
    FTisRemindTyp: Nullable<Integer>;
    
    [Column('PROTHZUSSYSTEM', [])]
    FProthzussystem: Nullable<Integer>;
    
    [Column('KEINRECALL', [TColumnProp.Required], 1)]
    FKeinrecall: string;
    
    [Column('ETI_ID', [])]
    FEtiId: Nullable<Int64>;
    
    [Column('ETI_CREATED', [])]
    FEtiCreated: Nullable<TDateTime>;
    
    [Column('ETI_MODIFIED', [])]
    FEtiModified: Nullable<TDateTime>;
  public
    property Patnr: Integer read FPatnr write FPatnr;
    property KasseId: Nullable<Integer> read FKasseId write FKasseId;
    property Kassekz: Nullable<string> read FKassekz write FKassekz;
    property Arbgeb: Nullable<string> read FArbgeb write FArbgeb;
    property Telarbgeb: Nullable<string> read FTelarbgeb write FTelarbgeb;
    property Versadr: Nullable<Integer> read FVersadr write FVersadr;
    property Abwadr: Nullable<Integer> read FAbwadr write FAbwadr;
    property Hausarztadr: Nullable<Integer> read FHausarztadr write FHausarztadr;
    property Ueberweiseradr: Nullable<Integer> read FUeberweiseradr write FUeberweiseradr;
    property Nachrichtl1adr: Nullable<Integer> read FNachrichtl1adr write FNachrichtl1adr;
    property Nachrichtl2adr: Nullable<Integer> read FNachrichtl2adr write FNachrichtl2adr;
    property Mahnen: Nullable<Integer> read FMahnen write FMahnen;
    property Bankeinzug: Nullable<Integer> read FBankeinzug write FBankeinzug;
    property Prothzuschuss: Nullable<Integer> read FProthzuschuss write FProthzuschuss;
    property Anamnese: Nullable<Integer> read FAnamnese write FAnamnese;
    property Einldat1: Nullable<TDateTime> read FEinldat1 write FEinldat1;
    property Einldat2: Nullable<TDateTime> read FEinldat2 write FEinldat2;
    property Kfozuschuss: Nullable<Integer> read FKfozuschuss write FKfozuschuss;
    property FreiwVers: Nullable<Integer> read FFreiwVers write FFreiwVers;
    property Versnr: Nullable<string> read FVersnr write FVersnr;
    property Kennz: Nullable<Integer> read FKennz write FKennz;
    property GlobPatnr: Nullable<Integer> read FGlobPatnr write FGlobPatnr;
    property Gebdat: Nullable<TDateTime> read FGebdat write FGebdat;
    property Geschlecht: Nullable<string> read FGeschlecht write FGeschlecht;
    property Titel: Nullable<string> read FTitel write FTitel;
    property Anrede: Nullable<string> read FAnrede write FAnrede;
    property Briefanrede: Nullable<string> read FBriefanrede write FBriefanrede;
    property Namenszusatz: Nullable<string> read FNamenszusatz write FNamenszusatz;
    property Nachname: Nullable<string> read FNachname write FNachname;
    property Vorname: Nullable<string> read FVorname write FVorname;
    property Soundexnachname: Nullable<string> read FSoundexnachname write FSoundexnachname;
    property Soundexvorname: Nullable<string> read FSoundexvorname write FSoundexvorname;
    property Strasse: Nullable<string> read FStrasse write FStrasse;
    property Lkz: Nullable<string> read FLkz write FLkz;
    property Plz: Nullable<string> read FPlz write FPlz;
    property Ort: Nullable<string> read FOrt write FOrt;
    property Postfach: Nullable<string> read FPostfach write FPostfach;
    property PsfPlz: Nullable<string> read FPsfPlz write FPsfPlz;
    property PsfOrt: Nullable<string> read FPsfOrt write FPsfOrt;
    property Telefon1: Nullable<string> read FTelefon1 write FTelefon1;
    property Telefon2: Nullable<string> read FTelefon2 write FTelefon2;
    property Fax: Nullable<string> read FFax write FFax;
    property Email: Nullable<string> read FEmail write FEmail;
    property Beruf: Nullable<string> read FBeruf write FBeruf;
    property Duzfreund: Nullable<string> read FDuzfreund write FDuzfreund;
    property Anzahlung: Nullable<Double> read FAnzahlung write FAnzahlung;
    property Stammbehandler: Nullable<string> read FStammbehandler write FStammbehandler;
    property Aufnahmedatum: Nullable<TDateTime> read FAufnahmedatum write FAufnahmedatum;
    property Rechbankkonto: Nullable<Integer> read FRechbankkonto write FRechbankkonto;
    property Archiviert: Nullable<string> read FArchiviert write FArchiviert;
    property Archivgesperrt: Nullable<string> read FArchivgesperrt write FArchivgesperrt;
    property Uebernahme: Nullable<string> read FUebernahme write FUebernahme;
    property Archiviertam: Nullable<TDateTime> read FArchiviertam write FArchiviertam;
    property Archiviertbenutzer: Nullable<string> read FArchiviertbenutzer write FArchiviertbenutzer;
    property Kontaktart: Nullable<Integer> read FKontaktart write FKontaktart;
    property Verstorben: Nullable<string> read FVerstorben write FVerstorben;
    property Sms1: Nullable<string> read FSms1 write FSms1;
    property Gebort: Nullable<string> read FGebort write FGebort;
    property Gebname: Nullable<string> read FGebname write FGebname;
    property VddsrzDefault: Nullable<string> read FVddsrzDefault write FVddsrzDefault;
    property Prenotification: Nullable<Integer> read FPrenotification write FPrenotification;
    property SperreTis: Nullable<string> read FSperreTis write FSperreTis;
    property SperreEmail: Nullable<string> read FSperreEmail write FSperreEmail;
    property SperreSms: Nullable<string> read FSperreSms write FSperreSms;
    property SperrePatinfo: Nullable<string> read FSperrePatinfo write FSperrePatinfo;
    property HatBeihilfe: Nullable<string> read FHatBeihilfe write FHatBeihilfe;
    property Zuzahlungsbefreit: Nullable<string> read FZuzahlungsbefreit write FZuzahlungsbefreit;
    property ZuzahlungsbefreitDatum: Nullable<TDateTime> read FZuzahlungsbefreitDatum write FZuzahlungsbefreitDatum;
    property NamenszusatzEgk: Nullable<string> read FNamenszusatzEgk write FNamenszusatzEgk;
    property Vorsatzwort: Nullable<string> read FVorsatzwort write FVorsatzwort;
    property StrasseEgk: Nullable<string> read FStrasseEgk write FStrasseEgk;
    property Hausnummer: Nullable<string> read FHausnummer write FHausnummer;
    property Anschriftenzusatz: Nullable<string> read FAnschriftenzusatz write FAnschriftenzusatz;
    property LkzEgk: Nullable<string> read FLkzEgk write FLkzEgk;
    property PlzEgk: Nullable<string> read FPlzEgk write FPlzEgk;
    property OrtEgk: Nullable<string> read FOrtEgk write FOrtEgk;
    property PsfLkz: Nullable<string> read FPsfLkz write FPsfLkz;
    property Infektionsrisiko: Nullable<string> read FInfektionsrisiko write FInfektionsrisiko;
    property TisRemind: Nullable<string> read FTisRemind write FTisRemind;
    property TisRemindSms: Nullable<string> read FTisRemindSms write FTisRemindSms;
    property TisRemindEmail: Nullable<string> read FTisRemindEmail write FTisRemindEmail;
    property TisRemindSmsVers: Nullable<string> read FTisRemindSmsVers write FTisRemindSmsVers;
    property TisRemindEmailVers: Nullable<string> read FTisRemindEmailVers write FTisRemindEmailVers;
    property TisRemindSmsRech: Nullable<string> read FTisRemindSmsRech write FTisRemindSmsRech;
    property TisRemindEmailRech: Nullable<string> read FTisRemindEmailRech write FTisRemindEmailRech;
    property TisRemindTyp: Nullable<Integer> read FTisRemindTyp write FTisRemindTyp;
    property Prothzussystem: Nullable<Integer> read FProthzussystem write FProthzussystem;
    property Keinrecall: string read FKeinrecall write FKeinrecall;
    property EtiId: Nullable<Int64> read FEtiId write FEtiId;
    property EtiCreated: Nullable<TDateTime> read FEtiCreated write FEtiCreated;
    property EtiModified: Nullable<TDateTime> read FEtiModified write FEtiModified;
  end;
  
  [Entity]
  [Table('PATINDIK')]
  [Id('FPatnr', TIdGenerator.None)]
  [Id('FIndinr', TIdGenerator.None)]
  [Model('PRAXIS')]
  [IdUnsavedValue(-1)]
  TPatindik = class
  private
    [Column('PATNR', [TColumnProp.Required])]
    FPatnr: Integer;
    
    [Column('INDINR', [TColumnProp.Required])]
    FIndinr: Integer;
  public
    property Patnr: Integer read FPatnr write FPatnr;
    property Indinr: Integer read FIndinr write FIndinr;
  end;
  
  [Entity]
  [Table('BEFUND')]
  [Id('FPatnr', TIdGenerator.None)]
  [Id('FDatum', TIdGenerator.None)]
  [Model('PRAXIS')]
  [IdUnsavedValue(-1)]
  TTableBefund = class
  private
    [Column('PATNR', [TColumnProp.Required])]
    FPatnr: Integer;
    
    [Column('DATUM', [TColumnProp.Required])]
    FDatum: TDateTime;
    
    [Column('ZAHNSTEIN', [])]
    FZahnstein: Nullable<Integer>;
    
    [Column('MUNDKRANKHEIT', [])]
    FMundkrankheit: Nullable<Integer>;
    
    [Column('SONSTBEFUND', [], 255)]
    FSonstbefund: Nullable<string>;
    
    [Column('PALOCKERUNG', [], 64)]
    FPalockerung: Nullable<string>;
    
    [Column('PATASCHENTIEFEN', [], 194)]
    FPataschentiefen: Nullable<string>;
    
    [Column('ENVIRONMENT', [], 64)]
    FEnvironment: Nullable<string>;
    
    [Column('ENVIJA', [], 1)]
    FEnvija: Nullable<string>;
    
    [Column('MESS2', [], 1)]
    FMess2: Nullable<string>;
    
    [Column('PATASCHENKOMMA', [], 192)]
    FPataschenkomma: Nullable<string>;
    
    [Column('PABOP', [], 32)]
    FPabop: Nullable<string>;
    
    [Column('GUMMYSMILE', [])]
    FGummysmile: Nullable<Integer>;
    
    [Column('PABOPEXTEND', [], 192)]
    FPabopextend: Nullable<string>;
    
    [Column('PAKNOCHENVERLUST', [])]
    FPaknochenverlust: Nullable<Integer>;
    
    [Column('ZIGARETTENKONSUM', [])]
    FZigarettenkonsum: Nullable<Integer>;
    
    [Column('SYSTEMGENFAKTOREN', [])]
    FSystemgenfaktoren: Nullable<Integer>;
    
    [Column('AENDVERWEIS', [])]
    FAendverweis: Nullable<Integer>;
    
    [Column('PABEFUND_ERFASST', [])]
    FPabefundErfasst: Nullable<Integer>;
    
    [Column('PAPLAQUE', [], 32)]
    FPaplaque: Nullable<string>;
  public
    property Patnr: Integer read FPatnr write FPatnr;
    property Datum: TDateTime read FDatum write FDatum;
    property Zahnstein: Nullable<Integer> read FZahnstein write FZahnstein;
    property Mundkrankheit: Nullable<Integer> read FMundkrankheit write FMundkrankheit;
    property Sonstbefund: Nullable<string> read FSonstbefund write FSonstbefund;
    property Palockerung: Nullable<string> read FPalockerung write FPalockerung;
    property Pataschentiefen: Nullable<string> read FPataschentiefen write FPataschentiefen;
    property Environment: Nullable<string> read FEnvironment write FEnvironment;
    property Envija: Nullable<string> read FEnvija write FEnvija;
    property Mess2: Nullable<string> read FMess2 write FMess2;
    property Pataschenkomma: Nullable<string> read FPataschenkomma write FPataschenkomma;
    property Pabop: Nullable<string> read FPabop write FPabop;
    property Gummysmile: Nullable<Integer> read FGummysmile write FGummysmile;
    property Pabopextend: Nullable<string> read FPabopextend write FPabopextend;
    property Paknochenverlust: Nullable<Integer> read FPaknochenverlust write FPaknochenverlust;
    property Zigarettenkonsum: Nullable<Integer> read FZigarettenkonsum write FZigarettenkonsum;
    property Systemgenfaktoren: Nullable<Integer> read FSystemgenfaktoren write FSystemgenfaktoren;
    property Aendverweis: Nullable<Integer> read FAendverweis write FAendverweis;
    property PabefundErfasst: Nullable<Integer> read FPabefundErfasst write FPabefundErfasst;
    property Paplaque: Nullable<string> read FPaplaque write FPaplaque;
  end;
  
  [Entity]
  [Table('ZAHNBEFU')]
  [Id('FPatnr', TIdGenerator.None)]
  [Id('FDatum', TIdGenerator.None)]
  [Id('FZahnnr', TIdGenerator.None)]
  [Model('PRAXIS')]
  [IdUnsavedValue(-1)]
  TZahnbefu = class
  private
    [Column('PATNR', [TColumnProp.Required])]
    FPatnr: Integer;
    
    [Column('DATUM', [TColumnProp.Required])]
    FDatum: TDateTime;
    
    [Column('ZAHNNR', [TColumnProp.Required])]
    FZahnnr: Integer;
    
    [Column('MILCHZAHN', [])]
    FMilchzahn: Nullable<Integer>;
    
    [Column('VORHANDEN', [])]
    FVorhanden: Nullable<Integer>;
    
    [Column('VZAHNZUSTAND', [])]
    FVzahnzustand: Nullable<Integer>;
    
    [Column('FZAHNZUSTAND', [])]
    FFzahnzustand: Nullable<Integer>;
    
    [Column('PROTHETIK', [])]
    FProthetik: Nullable<Integer>;
    
    [Column('ERKRANKUNG', [])]
    FErkrankung: Nullable<Integer>;
    
    [Column('WFLAGEN', [])]
    FWflagen: Nullable<Integer>;
    
    [Column('HELAGEN', [])]
    FHelagen: Nullable<Integer>;
    
    [Column('RELAGEN', [])]
    FRelagen: Nullable<Integer>;
    
    [Column('KALAGEN', [])]
    FKalagen: Nullable<Integer>;
    
    [Column('FULAGEN', [])]
    FFulagen: Nullable<Integer>;
    
    [Column('FUMATERIAL', [])]
    FFumaterial: Nullable<Integer>;
    
    [Column('INLAGEN', [])]
    FInlagen: Nullable<Integer>;
    
    [Column('INMATERIAL', [])]
    FInmaterial: Nullable<Integer>;
    
    [Column('WURZELN', [])]
    FWurzeln: Nullable<Integer>;
    
    [Column('KANAELE', [])]
    FKanaele: Nullable<Integer>;
    
    [Column('KFOZUSTAND', [])]
    FKfozustand: Nullable<Integer>;
    
    [Column('ZAHNTEXT', [TColumnProp.Lazy], 80)]
    [DBTypeMemo]
    FZahntext: TBlob;
    
    [Column('FUELLUNGEN', [TColumnProp.Lazy], 80)]
    [DBTypeMemo]
    FFuellungen: TBlob;
    
    [Column('FREMDBEFUNDE', [])]
    FFremdbefunde: Nullable<Int64>;
  public
    property Patnr: Integer read FPatnr write FPatnr;
    property Datum: TDateTime read FDatum write FDatum;
    property Zahnnr: Integer read FZahnnr write FZahnnr;
    property Milchzahn: Nullable<Integer> read FMilchzahn write FMilchzahn;
    property Vorhanden: Nullable<Integer> read FVorhanden write FVorhanden;
    property Vzahnzustand: Nullable<Integer> read FVzahnzustand write FVzahnzustand;
    property Fzahnzustand: Nullable<Integer> read FFzahnzustand write FFzahnzustand;
    property Prothetik: Nullable<Integer> read FProthetik write FProthetik;
    property Erkrankung: Nullable<Integer> read FErkrankung write FErkrankung;
    property Wflagen: Nullable<Integer> read FWflagen write FWflagen;
    property Helagen: Nullable<Integer> read FHelagen write FHelagen;
    property Relagen: Nullable<Integer> read FRelagen write FRelagen;
    property Kalagen: Nullable<Integer> read FKalagen write FKalagen;
    property Fulagen: Nullable<Integer> read FFulagen write FFulagen;
    property Fumaterial: Nullable<Integer> read FFumaterial write FFumaterial;
    property Inlagen: Nullable<Integer> read FInlagen write FInlagen;
    property Inmaterial: Nullable<Integer> read FInmaterial write FInmaterial;
    property Wurzeln: Nullable<Integer> read FWurzeln write FWurzeln;
    property Kanaele: Nullable<Integer> read FKanaele write FKanaele;
    property Kfozustand: Nullable<Integer> read FKfozustand write FKfozustand;
    property Zahntext: TBlob read FZahntext write FZahntext;
    property Fuellungen: TBlob read FFuellungen write FFuellungen;
    property Fremdbefunde: Nullable<Int64> read FFremdbefunde write FFremdbefunde;
  end;
  
implementation

initialization
  RegisterEntity(TAnam);
  RegisterEntity(TAufgaben);
  RegisterEntity(TTableBefund);
  RegisterEntity(TDauerdiagnosen);
  RegisterEntity(TIndikat);
  RegisterEntity(TKasse);
  RegisterEntity(TLeistung);
  RegisterEntity(TNotizp);
  RegisterEntity(TPatient);
  RegisterEntity(TPatindik);
  RegisterEntity(TZahnbefu);

end.
