﻿unit F02.Uebersicht.Form;

interface

uses
  System.SysUtils, System.Classes,
  Web,
  Vcl.Menus, Vcl.StdCtrls, Vcl.Imaging.pngimage, Vcl.Controls,
  WEBLib.Graphics, WEBLib.Controls, WEBLib.Forms, WEBLib.Dialogs, WEBLib.Menus, WEBLib.StdCtrls, WEBLib.Buttons,
  WEBLib.ExtCtrls,
  Base.Form.Status, WEBLib.Lists, WEBLib.DBCtrls, Data.DB, WEBLib.DB, WEBLib.Grids, Vcl.Grids, WEBLib.WebCtrls,
  Stammdaten.Patient;

type
  TFormUebersicht = class(TFormBaseStatus)
    LabelPatient: TWebLabel;
    ButtonTest: TWebHTMLDiv;
    ButtonTabBehandlung: TWebHTMLDiv;
    procedure buttonBackClick(Sender: TObject);
    procedure ButtonTabBehandlungClick(Sender: TObject);
    procedure ButtonWartezimmerClick(Sender: TObject);
    procedure WebFormCreate(Sender: TObject);
  private
    FPatientInfo: TPatientInfo;
    procedure SetPatientInfo(const Value: TPatientInfo);
    { Private-Deklarationen }
  public
    property PatientInfo: TPatientInfo read FPatientInfo write SetPatientInfo;
  end;



implementation

uses
  Main.DM, F00.Login.Form, App.Controller, DX.WEBLib.Logger;

{$R *.dfm}

procedure TFormUebersicht.buttonBackClick(Sender: TObject);
begin
  inherited;
  Close;
end;

procedure TFormUebersicht.ButtonTabBehandlungClick(Sender: TObject);
begin
  inherited;
  AppController.ShowBehandlungBefund(PatientInfo);
  Close;
end;

procedure TFormUebersicht.ButtonWartezimmerClick(Sender: TObject);
begin
  inherited;
  //Zurück ins Wartezimmer
  AppController.ShowWartezimmer;
  Close;
end;

procedure TFormUebersicht.SetPatientInfo(const Value: TPatientInfo);
begin
  FPatientInfo := Value;

  if Assigned(FPatientInfo) then
  begin
    LabelPatient.Caption := FPatientInfo.Patient.Name + ', ' + FPatientInfo.Patient.VorName;
    DXLog('Patient: ' + LabelPatient.Caption);
  end;
end;

procedure TFormUebersicht.WebFormCreate(Sender: TObject);
begin
  Title := 'Übersicht';
  inherited;
end;

end.

