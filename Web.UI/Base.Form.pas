﻿unit Base.Form;

interface

uses
  System.SysUtils, System.Classes, JS, Web,
  WEBLib.Graphics, WEBLib.Controls, WEBLib.Forms, WEBLib.Dialogs,
  DX.WEBLib.SysUtils;

type
  /// <summary>
  /// Vorlage für alle Formulare der App.
  ///  - Font auf "Frutiger LT Std 45 Light, Inter, Helvetica Neue, Helvetica, Arial, sans-serif"
  ///  - Title Property
  /// </summary>
  TFormBase = class abstract(TWebForm)
    procedure WebFormClose(Sender: TObject; var Action: TCloseAction);
    procedure WebFormCreate(Sender: TObject);
  private
    FTitle: string;
    { Private-Deklarationen }
  public
    property Title: string read FTitle write FTitle;
  end;

implementation
uses
  App.Controller;

{$R *.dfm}

procedure TFormBase.WebFormClose(Sender: TObject; var Action: TCloseAction);
begin
  Action := TCloseAction.caFree;
end;

procedure TFormBase.WebFormCreate(Sender: TObject);
begin
  if FTitle > '' then
  begin
    Caption := Format('%s - %s', [AppController.AppTitle, FTitle]);
  end
  else
  begin
    Caption := Format('%s', [AppController.AppTitle]);
  end;
end;

end.

