﻿unit F04.Test.Behandlung.Befund.Form;

interface

uses
  System.SysUtils, System.Classes,
  Web,

  Vcl.Controls,

  WEBLib.Graphics, WEBLib.Controls,
  WEBLib.Forms, WEBLib.Dialogs, WEBLib.WebCtrls,

  Base.Form.Status,

  Stammdaten.Patient, Vcl.StdCtrls, WEBLib.StdCtrls, WEBLib.ExtCtrls;

type
  TFormBehandlungBefundTest = class(TFormBaseStatus)
    ButtonTabUebersicht: TWebHTMLDiv;
    procedure WebFormCreate(Sender: TObject);
    procedure ButtonTabUebersichtClick(Sender: TObject);
  private
    FPatientInfo: TPatientInfo;
    procedure SetPatientInfo(const Value: TPatientInfo);
    { Private declarations }
  public

    property PatientInfo: TPatientInfo read FPatientInfo write SetPatientInfo;
    { Public declarations }
  end;

implementation

{$R *.dfm}

uses
  App.Controller;

procedure TFormBehandlungBefundTest.WebFormCreate(Sender: TObject);
var
  LOnline: Boolean;
begin
  //
  LOnline := Application.IsOnline;
end;

procedure TFormBehandlungBefundTest.ButtonTabUebersichtClick(Sender: TObject);
begin
  AppController.ShowFormUebersicht(PatientInfo);
  Close;
end;

procedure TFormBehandlungBefundTest.SetPatientInfo(const Value: TPatientInfo);
begin
  FPatientInfo := Value;

  if Assigned(FPatientInfo) then
  begin
    // LabelPatient.Caption := FPatientInfo.Patient.Name + ', ' + FPatientInfo.Patient.VorName;
  end;
end;

end.

