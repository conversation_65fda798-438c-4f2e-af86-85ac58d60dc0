﻿unit F04.Test.Behandlung.Befund.Form;

interface

uses
  System.SysUtils, System.Classes,
  Web,

  Vcl.Controls,

  WEBLib.Graphics, WEBLib.Controls,
  WEBLib.Forms, WEBLib.Dialogs, WEBLib.WebCtrls,

  Base.Form.Status,

  Stammdaten.Patient, Vcl.StdCtrls, WEBLib.StdCtrls, WEBLib.ExtCtrls;

type
  TFormBehandlungBefundTest = class(TFormBaseStatus)
    ButtonTabUebersicht: TWebHTMLDiv;
    procedure WebFormCreate(Sender: TObject);
    procedure ButtonTabUebersichtClick(Sender: TObject);
  private
    FPatient: TPatient;
    procedure SetPatient(const Value: TPatient);
    { Private declarations }
  public

    property Patient: TPatient read FPatient write SetPatient;
    { Public declarations }
  end;

implementation

{$R *.dfm}

uses
  App.Controller;

procedure TFormBehandlungBefundTest.WebFormCreate(Sender: TObject);
var
  LOnline: Boolean;
begin
  //
  LOnline := Application.IsOnline;
end;

procedure TFormBehandlungBefundTest.ButtonTabUebersichtClick(Sender: TObject);
begin
  AppController.ShowFormUebersicht(Patient);
  Close;
end;

procedure TFormBehandlungBefundTest.SetPatient(const Value: TPatient);
begin
  FPatient := Value;

  // LabelPatient.Caption := FPatient.Name + ', ' + FPatient.VorName;
end;

end.

