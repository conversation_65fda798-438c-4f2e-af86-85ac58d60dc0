# Git ignore-list optimized for Delphi projects

# General Ignore Patterns
## Node artifact files
node_modules/
dist/

## Compiled Java class files
*.class

## Compiled Python bytecode
*.py[cod]

## Log files
*.log

## Package files
*.jar

## Unit test reports
TEST*.xml

## Generated by MacOS
.DS_Store

## Generated by Windows
Thumbs.db

## Large media files
*.mp4
*.tiff
*.avi
*.flv
*.mov
*.wmv

## Applications
*.app
*.exe
*.war

# IDE and Build Tools
## JetBrains IDE
.idea/

## Maven
target/

# SCM/versioning files
.hg/
.svn/

# Boss dependency manager vendor folder
modules/

# Delphi Compiler-Generated Files
## Compiler-generated binaries (safe to delete)
*.dll
*.bpl
*.bpi
*.dcp
*.so
*.apk
*.drc
*.map
*.dres
*.rsm
*.tds
*.dcu
*.lib
*.a
*.o
*.ocx

## Autogenerated files (duplicated info)
*.cfg
*.hpp
*Resource.rc

## Local files (user-specific info)
*.local
*.identcache
*.projdata
*.tvsconfig
*.dsk
*.user

## Backup and Autosave files
*.~bpl
*.~dsk
*.~pas
*.~dfm
*.~*
*.save
*.cbk

## Unit State (cache) files
*.dcu.*
*.state

# Delphi History and Backups
__history/
__recovery/

# Delphi Specific Build Directories
Release/
Debug/
DCU/

# Default Platform Output Directories
Linux64/
OSX32/
OSX64/
Win32/
Win64/
Android/

# Project Intermediate Files
*.il*
*.tlib*

# Delphi Related Temporary Build Folders
__build/

# Miscellaneous Files
~$*.*
_Release-Binaries

# Delphi-Specific Ignore Patterns
## Resource files (manifest, project icon, version info)
*.res

## Type library file (binary)
*.tlb

## Diagram Portfolio file (used up to Delphi 7)
*.ddp

## C++ Object Files
*.obj

##TMS WebCore Output
TMSWeb/
