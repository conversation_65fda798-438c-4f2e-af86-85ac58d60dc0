/* screen - 01-wartezimmer */

.x01-wartezimmer {
  align-items: flex-start;
  background-color: var(--concrete);
  display: flex;
  flex-direction: column;
  gap: 0.52vw;
  max-width: 100.0vw;
  min-height: 100vh;
  position: relative;
}

.x01-wartezimmer .container-1 {
  align-items: center;
  display: flex;
  flex: 1;
  flex-direction: column;
  flex-grow: 1;
  gap: 0.83vw;
  max-width: 100.0vw;
  padding: 1.25vw;
  position: relative;
  width: 100%;
}

.x01-wartezimmer .header-1 {
  align-items: flex-start;
  display: inline-flex;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 0.52vw;
  position: relative;
}

.x01-wartezimmer .header-2 {
  align-items: center;
  display: flex;
  height: 2.5vw;
  justify-content: space-between;
  position: relative;
  width: 97.5vw;
}

.x01-wartezimmer .button {
  align-items: center;
  display: flex;
  gap: 0.99vw;
  height: 2.5vw;
  justify-content: center;
  position: relative;
  width: 2.5vw;
}

.x01-wartezimmer .badge {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.42vw;
  position: relative;
}

.x01-wartezimmer .frame-683 {
  align-items: center;
  background-color: var(--magenta);
  border-radius: 0.83vw;
  display: flex;
  flex-direction: column;
  gap: 0.52vw;
  height: 1.67vw;
  justify-content: center;
  padding: 0.42vw 0.31vw;
  position: relative;
  width: 1.67vw;
}

.x01-wartezimmer .header-3 {
  align-items: center;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  gap: 4.17vw;
  position: relative;
  width: 100%;
}

.x01-wartezimmer .container-2 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.42vw;
  position: relative;
}

.x01-wartezimmer .heading-2 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  height: 2.08vw;
  justify-content: center;
  position: relative;
}

.x01-wartezimmer .heading-3 {
  color: var(--midnight-blue);
  font-family: var(--font-family-inter);
  font-size: var(--font-size-xl);
  font-weight: 400;
  letter-spacing: 0;
  line-height: 2.0vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x01-wartezimmer .body {
  align-items: flex-start;
  display: flex;
  flex: 1;
  flex-grow: 1;
  gap: 0.83vw;
  max-width: 97.5vw;
  position: relative;
  width: 100%;
}

.x01-wartezimmer .content-box {
  align-items: flex-start;
  align-self: stretch;
  border-radius: 0.42vw;
  display: flex;
  flex: 1;
  flex-direction: column;
  flex-grow: 1;
  position: relative;
}

.x01-wartezimmer .tab-items {
  background-color: var(--mercury);
  border-radius: 0.42vw 0.0vw 0.0vw 0.0vw;
  display: inline-flex;
  gap: 0.21vw;
  height: 2.08vw;
  padding: 0.0vw 0.83vw;
}

.x01-wartezimmer .label {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  height: 1.04vw;
  position: relative;
}

.x01-wartezimmer .label-1 {
  letter-spacing: 0;
  line-height: 1.0vw;
  margin-top: -0.03vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x01-wartezimmer .tab-items-1 {
  align-items: center;
  background-color: var(--white);
  border-radius: 0.0vw 0.42vw 0.0vw 0.0vw;
  display: flex;
  flex: 1;
  flex-grow: 1;
  gap: 0.21vw;
  height: 2.08vw;
  padding: 0.0vw 0.83vw;
  position: relative;
}

.x01-wartezimmer .label-2 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  height: 1.04vw;
  position: relative;
}

.x01-wartezimmer .label-3 {
  letter-spacing: 0;
  line-height: 1.0vw;
  margin-top: -0.03vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x01-wartezimmer .container-3 {
  align-items: flex-start;
  align-self: stretch;
  background-color: var(--white);
  border-radius: 0.0vw 0.0vw 0.31vw 0.31vw;
  display: flex;
  flex: 1;
  flex-grow: 1;
  gap: 0.83vw;
  padding: 0.83vw;
  position: relative;
  width: 100%;
}

.x01-wartezimmer .body-1::-webkit-scrollbar {
  display: none;
  width: 0;
}

.x01-wartezimmer .body-1 {
  align-items: flex-start;
  align-self: stretch;
  display: flex;
  flex: 1;
  flex-direction: column;
  flex-grow: 1;
  overflow-y: scroll;
  padding: 0.83vw;
  position: relative;
}

.x01-wartezimmer .list {
  align-items: flex-start;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  flex-direction: column;
  position: relative;
  width: 100%;
}

.x01-wartezimmer .two-line-list {
  align-items: flex-start;
  align-self: stretch;
  border-radius: 0.31vw;
  display: flex;
  flex: 0 0 auto;
  justify-content: space-between;
  min-width: 10.1vw;
  padding: 0.42vw;
  position: relative;
  width: 100%;
}

.x01-wartezimmer .frame-5 {
  align-items: flex-start;
  display: flex;
  gap: 0.42vw;
  position: relative;
  width: 15.1vw;
}

.x01-wartezimmer .text-container {
  height: 2.34vw;
  position: relative;
  width: 7.71vw;
}

.x01-wartezimmer .list-item-title-1 {
  line-height: 1.25vw;
}

.x01-wartezimmer .secondary-text {
  letter-spacing: 0;
  line-height: 1.09vw;
  margin-right: -5.89vw;
  margin-top: -0.05vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x01-wartezimmer .two-line-list-2 {
  align-self: stretch;
  background-color: var(--lily-white);
  flex: 0 0 auto;
  justify-content: space-between;
  width: 100%;
}

.x01-wartezimmer .list-item-title-2 {
  line-height: 1.25vw;
  margin-right: -0.21vw;
}

.x01-wartezimmer .secondary-text-1 {
  line-height: 1.09vw;
  margin-right: -2.97vw;
}

.x01-wartezimmer .list-item-title-3 {
  line-height: 1.25vw;
}

.x01-wartezimmer .secondary-text-2 {
  line-height: 1.09vw;
  margin-right: -5.36vw;
}

.x01-wartezimmer .list-item-title-4 {
  line-height: 1.25vw;
}

.x01-wartezimmer .secondary-text-3 {
  line-height: 1.09vw;
  margin-right: -4.74vw;
}

.x01-wartezimmer .list-item-title-5 {
  line-height: 1.25vw;
}

.x01-wartezimmer .secondary-text-4 {
  line-height: 1.09vw;
  margin-right: -4.43vw;
}

.x01-wartezimmer .list-item-title-6 {
  line-height: 1.25vw;
}

.x01-wartezimmer .secondary-text-5 {
  line-height: 1.09vw;
  margin-right: -5.62vw;
}

.x01-wartezimmer .devider {
  align-self: stretch;
  object-fit: cover;
  position: relative;
  width: 0.05vw;
}

.x01-wartezimmer .container-stammdaten {
  align-items: flex-end;
  align-self: stretch;
  display: flex;
  flex: 1;
  flex-direction: column;
  flex-grow: 1;
  gap: 0.83vw;
  justify-content: center;
  position: relative;
}

.x01-wartezimmer .content-box-1 {
  align-items: flex-start;
  align-self: stretch;
  background-color: var(--white);
  border-radius: 0.42vw;
  display: flex;
  flex: 1;
  flex-direction: column;
  flex-grow: 1;
  gap: 0.83vw;
  padding: 0.83vw;
  position: relative;
  width: 100%;
}

.x01-wartezimmer .header {
  align-items: center;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  justify-content: space-between;
  position: relative;
  width: 100%;
}

.x01-wartezimmer .heading-4 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  height: 1.04vw;
  position: relative;
}

.x01-wartezimmer .heading-5 {
  color: var(--midnight-blue);
  font-family: var(--font-family-frutiger_lt_std-semibold);
  font-size: var(--font-size-m);
  font-weight: 600;
  letter-spacing: 0;
  line-height: 1.0vw;
  margin-top: -0.03vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x01-wartezimmer .container-4 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.83vw;
  justify-content: flex-end;
  position: relative;
}

.x01-wartezimmer .nachname-name {
  color: var(--black);
  font-family: var(--font-family-frutiger_lt_std-45light);
  font-size: var(--font-size-l);
  font-weight: 300;
  letter-spacing: 0;
  line-height: 1.88vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x01-wartezimmer .bersicht {
  align-items: flex-start;
  align-self: stretch;
  background-color: var(--white);
  display: flex;
  flex: 1;
  flex-direction: column;
  flex-grow: 1;
  padding: 0.0vw 0.0vw 4.48vw;
  position: relative;
  width: 100%;
}

.x01-wartezimmer .container {
  align-items: center;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  gap: 0.83vw;
  position: relative;
  width: 100%;
}

.x01-wartezimmer .two-line-list-1 {
  flex: 1;
  flex-grow: 1;
  gap: 14.53vw;
}

.x01-wartezimmer .frame-5-1 {
  display: inline-flex;
  flex: 0 0 auto;
}

.x01-wartezimmer .secondary-text-6 {
  line-height: 1.25vw;
}

.x01-wartezimmer .frame-5-2 {
  display: flex;
  flex: 1;
  flex-grow: 1;
}

.x01-wartezimmer .list-item-title-7 {
  line-height: 1.09vw;
  margin-right: -0.36vw;
}

.x01-wartezimmer .secondary-text-7 {
  line-height: 1.25vw;
}

.x01-wartezimmer .secondary-text-8 {
  line-height: 1.25vw;
}

.x01-wartezimmer .secondary-text-9 {
  line-height: 1.25vw;
}

.x01-wartezimmer .secondary-text-10 {
  line-height: 1.25vw;
}

.x01-wartezimmer .secondary-text-11 {
  line-height: 1.25vw;
}

.x01-wartezimmer .divider {
  align-items: center;
  display: flex;
  gap: 0.52vw;
  height: 0.83vw;
  justify-content: center;
  padding: 0.0vw 0.21vw;
  position: relative;
  width: 30.42vw;
}

.x01-wartezimmer .line {
  flex: 1;
  flex-grow: 1;
  height: 0.05vw;
  object-fit: cover;
  position: relative;
}

.x01-wartezimmer .heading {
  align-items: center;
  display: inline-flex;
  height: 1.04vw;
  position: relative;
}

.x01-wartezimmer .heading-1 {
  letter-spacing: 0.05vw;
  line-height: 0.88vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x01-wartezimmer .secondary-text-12 {
  line-height: 1.25vw;
  margin-right: -1.3vw;
}

.x01-wartezimmer .heading-7 {
  letter-spacing: 0;
  line-height: 1.0vw;
  margin-top: -0.03vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x01-wartezimmer .action-group-1 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.21vw;
  height: 1.25vw;
  justify-content: center;
  padding: 0.21vw 0.0vw;
  position: relative;
}

.x01-wartezimmer .button-2 {
  align-items: center;
  border-radius: 0.31vw;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.42vw;
  height: 1.25vw;
  justify-content: center;
  margin-bottom: -0.21vw;
  margin-top: -0.21vw;
  padding: 0.62vw 0.83vw;
  position: relative;
}

.x01-wartezimmer .button-3 {
  letter-spacing: 0;
  line-height: 1.0vw;
  margin-bottom: -0.44vw;
  margin-top: -0.55vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x01-wartezimmer .button-4 {
  align-items: center;
  background-color: var(--midnight-blue);
  border-radius: 0.31vw;
  cursor: pointer;
  display: inline-flex;
  gap: 0.21vw;
  height: 2.92vw;
  justify-content: center;
  padding: 0.62vw 0.83vw;
  position: relative;
}

.x01-wartezimmer .label-4 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  height: 1.04vw;
  position: relative;
}

.x01-wartezimmer .label-5 {
  letter-spacing: 0;
  line-height: 1.0vw;
  margin-top: -0.03vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x01-wartezimmer .frame-5-3 {
  align-items: flex-start;
  gap: 0.42vw;
  position: relative;
}

.x01-wartezimmer .list-item-title-8 {
  letter-spacing: 0;
  margin-top: -0.05vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x01-wartezimmer .secondary-text-13 {
  letter-spacing: 0;
  margin-top: -0.05vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x01-wartezimmer .two-line-list-3 {
  align-items: flex-start;
  border-radius: 0.31vw;
  display: flex;
  min-width: 10.1vw;
  padding: 0.42vw;
  position: relative;
}
