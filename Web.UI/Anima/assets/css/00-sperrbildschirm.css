/* screen - 00-sperrbildschirm */

.x00-sperrbildschirm {
  align-items: flex-start;
  background-color: var(--concrete);
  display: flex;
  flex-direction: column;
  gap: 0.52vw;
  height: 56.25vw;
  max-width: 100.0vw;
  position: relative;
}

.x00-sperrbildschirm .container-1 {
  height: 56.25vw;
  position: relative;
  width: 100.0vw;
}

.x00-sperrbildschirm .header {
  align-items: flex-start;
  display: flex;
  height: 2.5vw;
  left: 1.25vw;
  min-width: 97.5vw;
  position: absolute;
  top: 1.25vw;
}

.x00-sperrbildschirm .header-1 {
  align-items: center;
  display: flex;
  justify-content: space-between;
  position: relative;
  width: 97.5vw;
}

.x00-sperrbildschirm .badge {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.42vw;
  position: relative;
}

.x00-sperrbildschirm .frame-683 {
  align-items: center;
  background-color: var(--magenta);
  border-radius: 0.83vw;
  display: flex;
  flex-direction: column;
  gap: 0.52vw;
  height: 1.67vw;
  justify-content: center;
  padding: 0.42vw 0.31vw;
  position: relative;
  width: 1.67vw;
}

.x00-sperrbildschirm .icon-chevron-down {
  height: 1.25vw;
  position: relative;
  width: 1.25vw;
}

.x00-sperrbildschirm .login-form {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  gap: 2.08vw;
  justify-content: center;
  left: 29.17vw;
  position: absolute;
  top: 18.12vw;
  width: 41.67vw;
}

.x00-sperrbildschirm .container-2 {
  align-items: flex-start;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 1.25vw;
  position: relative;
  width: 100%;
}

.x00-sperrbildschirm .text-container {
  align-items: flex-start;
  display: flex;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 0.42vw;
  position: relative;
  width: 26.25vw;
}

.x00-sperrbildschirm .heading {
  align-items: center;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  position: relative;
  width: 100%;
}

.x00-sperrbildschirm .heading-1 {
  color: var(--midnight-blue);
  font-family: var(--font-family-inter);
  font-size: var(--font-size-xxl);
  font-weight: 400;
  letter-spacing: 0;
  line-height: 2.5vw;
  margin-top: -0.05vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x00-sperrbildschirm .paragraph-1 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  justify-content: center;
  position: relative;
}

.x00-sperrbildschirm .paragraph-2 {
  color: var(--midnight-blue);
  font-weight: 400;
  line-height: 1.25vw;
  margin-top: -0.05vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x00-sperrbildschirm .input-field {
  align-items: flex-start;
  align-self: stretch;
  display: flex;
  flex-direction: column;
  height: 2.92vw;
  position: relative;
  width: 100%;
}

.x00-sperrbildschirm .container {
  align-items: center;
  align-self: stretch;
  background-color: var(--white);
  border: 0.05vw solid;
  border-color: var(--mercury);
  border-radius: 0.31vw;
  display: flex;
  flex: 1;
  flex-grow: 1;
  gap: 0.83vw;
  padding: 0.42vw;
  position: relative;
  width: 100%;
}

.x00-sperrbildschirm .text {
  align-items: center;
  display: flex;
  flex: 1;
  flex-grow: 1;
  position: relative;
}

.x00-sperrbildschirm .paragraph {
  align-items: center;
  display: flex;
  flex: 1;
  flex-grow: 1;
  justify-content: center;
  position: relative;
}

.x00-sperrbildschirm .button {
  align-items: center;
  background-color: var(--midnight-blue);
  border-radius: 0.31vw;
  cursor: pointer;
  display: inline-flex;
  gap: 0.21vw;
  height: 2.08vw;
  justify-content: center;
  padding: 0.0vw 0.62vw;
  position: relative;
}

.x00-sperrbildschirm .label {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  height: 1.04vw;
  position: relative;
}

.x00-sperrbildschirm .label-1 {
  letter-spacing: 0;
  line-height: 1.0vw;
  margin-top: -0.03vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}
