/* screen - 02-uebersicht */

.x02-uebersicht {
  align-items: flex-start;
  background-color: var(--concrete);
  display: flex;
  flex-direction: column;
  max-width: 100.0vw;
  min-height: 100vh;
  position: relative;
}

.x02-uebersicht .container-6 {
  align-items: flex-start;
  display: flex;
  flex: 1;
  flex-direction: column;
  flex-grow: 1;
  gap: 0.83vw;
  max-width: 100.0vw;
  padding: 1.25vw;
  position: relative;
  width: 100%;
}

.x02-uebersicht .header {
  align-items: flex-start;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 0.83vw;
  position: relative;
  width: 100%;
}

.x02-uebersicht .button {
  align-items: center;
  display: flex;
  gap: 0.99vw;
  height: 2.5vw;
  justify-content: center;
  position: relative;
  width: 2.5vw;
}

.x02-uebersicht .badge-1 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.42vw;
  position: relative;
}

.x02-uebersicht .frame-683-2 {
  background-color: var(--magenta);
  border-radius: 0.83vw;
  gap: 0.52vw;
  height: 1.67vw;
  padding: 0.42vw 0.31vw;
  width: 1.67vw;
}

.x02-uebersicht .container-7 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.42vw;
  position: relative;
}

.x02-uebersicht .heading-4 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  height: 2.08vw;
  justify-content: center;
  position: relative;
}

.x02-uebersicht .heading-5 {
  color: var(--midnight-blue);
  font-family: var(--font-family-inter);
  font-size: var(--font-size-xl);
  font-weight: 400;
  letter-spacing: 0;
  line-height: 2.0vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x02-uebersicht .divider {
  align-items: center;
  border-radius: 0.42vw;
  display: flex;
  gap: 0.52vw;
  height: 2.08vw;
  justify-content: center;
  overflow: hidden;
  padding: 0.62vw 0.0vw;
  position: relative;
  width: 0.83vw;
}

.x02-uebersicht .line {
  align-self: stretch;
  object-fit: cover;
  position: relative;
  width: 0.05vw;
}

.x02-uebersicht .paragraph-4 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  justify-content: center;
  position: relative;
}

.x02-uebersicht .paragraph {
  color: var(--midnight-blue);
  font-weight: 400;
  line-height: 1.25vw;
  margin-top: -0.05vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x02-uebersicht .paragraph-5 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  position: relative;
}

.x02-uebersicht .button-3 {
  align-items: center;
  border-radius: 0.31vw;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.21vw;
  height: 1.67vw;
  justify-content: center;
  padding: 0.62vw 0.21vw;
  position: relative;
}

.x02-uebersicht .icon-1 {
  height: 1.25vw;
  margin-bottom: -0.42vw;
  margin-top: -0.42vw;
  position: relative;
  width: 1.25vw;
}

.x02-uebersicht .label {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  height: 1.04vw;
  margin-bottom: -0.31vw;
  margin-top: -0.31vw;
  position: relative;
}

.x02-uebersicht .label-2 {
  color: #ff0000;
  font-family: var(--font-family-inter);
  font-size: var(--font-size-m);
  font-weight: 500;
  letter-spacing: 0;
  line-height: 1.0vw;
  margin-top: -0.03vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x02-uebersicht .badge-2 {
  height: 1.67vw;
  position: relative;
  width: 1.67vw;
}

.x02-uebersicht .button-4 {
  align-items: center;
  display: flex;
  gap: 0.79vw;
  height: 1.67vw;
  justify-content: center;
  padding: 0.42vw 12.0.42vw;
  position: relative;
  width: 1.67vw;
}

.x02-uebersicht .icon-plus {
  height: 1.0vw;
  margin-bottom: -0.08vw;
  margin-left: -0.33vw;
  margin-right: -0.33vw;
  margin-top: -0.08vw;
  position: relative;
  width: 1.0vw;
}

.x02-uebersicht .frame-1011 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.83vw;
  position: relative;
}

.x02-uebersicht .dropdown {
  align-items: center;
  background-color: var(--white);
  border-radius: 0.21vw;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.42vw;
  height: 1.67vw;
  padding: 0.0vw 0.42vw;
  position: relative;
}

.x02-uebersicht .container {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.21vw;
  position: relative;
}

.x02-uebersicht .paragraph-1 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  padding: 0.0vw 0.83vw 0.0vw 0.0vw;
  position: relative;
}

.x02-uebersicht .icon-chevron-down {
  height: 1.04vw;
  position: relative;
  width: 1.04vw;
}

.x02-uebersicht .stopwatch {
  align-items: center;
  background-color: var(--midnight-blue);
  border-radius: 0.31vw;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.21vw;
  height: 2.08vw;
  justify-content: center;
  padding: 0.52vw 0.62vw;
  position: relative;
}

.x02-uebersicht .icon-play {
  height: 1.25vw;
  margin-bottom: -0.1vw;
  margin-top: -0.1vw;
  position: relative;
  width: 1.25vw;
}

.x02-uebersicht .timer {
  align-items: center;
  background-color: #ffffff1a;
  border-radius: 0.21vw;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.42vw;
  height: 1.25vw;
  justify-content: center;
  margin-bottom: -0.1vw;
  margin-top: -0.1vw;
  padding: 0.21vw;
  position: relative;
}

.x02-uebersicht .label-3 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  height: 1.04vw;
  margin-bottom: -0.1vw;
  margin-top: -0.1vw;
  position: relative;
}

.x02-uebersicht .label-4 {
  color: #cccccc;
  font-family: var(--font-family-inter);
  font-size: var(--font-size-m);
  font-weight: 500;
  letter-spacing: 0;
  line-height: 1.0vw;
  margin-top: -0.03vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x02-uebersicht .label-5 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  height: 1.04vw;
  position: relative;
}

.x02-uebersicht .label-6 {
  letter-spacing: 0;
  line-height: 1.0vw;
  margin-top: -0.03vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x02-uebersicht .body {
  align-items: flex-start;
  align-self: stretch;
  display: flex;
  flex: 1;
  flex-direction: column;
  flex-grow: 1;
  gap: 0.42vw;
  position: relative;
  width: 100%;
}

.x02-uebersicht .bersicht-1 {
  align-items: center;
  border-bottom-style: solid;
  border-bottom-width: 0.1vw;
  border-color: var(--magenta);
  display: inline-flex;
  flex: 0 0 auto;
  height: 1.67vw;
  justify-content: center;
  padding: 0.52vw 0.0vw;
  position: relative;
}

.x02-uebersicht .label-1 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  height: 1.04vw;
  margin-bottom: -0.21vw;
  margin-top: -0.21vw;
  position: relative;
}

.x02-uebersicht .bersicht-2 {
  align-items: center;
  cursor: pointer;
  display: inline-flex;
  flex: 0 0 auto;
  height: 1.67vw;
  justify-content: center;
  padding: 0.52vw 0.0vw;
  position: relative;
}

.x02-uebersicht .label-8 {
  letter-spacing: 0;
  line-height: 1.0vw;
  margin-top: -0.03vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x02-uebersicht .left {
  align-items: center;
  align-self: stretch;
  display: flex;
  flex: 1;
  flex-grow: 1;
  gap: 0.52vw;
  position: relative;
}

.x02-uebersicht .area-header {
  align-items: center;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  justify-content: space-between;
  position: relative;
  width: 100%;
}

.x02-uebersicht .heading-7 {
  color: var(--midnight-blue);
  font-family: var(--font-family-frutiger_lt_std-semibold);
  font-size: var(--font-size-m);
  font-weight: 600;
  letter-spacing: 0;
  line-height: 1.0vw;
  margin-top: -0.03vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x02-uebersicht .container-1 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.83vw;
  justify-content: flex-end;
  position: relative;
}

.x02-uebersicht .icon-2 {
  height: 1.25vw;
  margin-bottom: -0.52vw;
  margin-left: -0.83vw;
  margin-right: -0.83vw;
  margin-top: -0.52vw;
  position: relative;
  width: 1.25vw;
}

.x02-uebersicht .bersicht-4 {
  align-items: flex-start;
  align-self: stretch;
  background-color: var(--white);
  display: flex;
  flex: 1;
  flex-direction: column;
  flex-grow: 1;
  padding: 0.0vw 0.0vw 4.48vw;
  position: relative;
  width: 100%;
}

.x02-uebersicht .container-2 {
  align-items: center;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  gap: 0.83vw;
  position: relative;
  width: 100%;
}

.x02-uebersicht .two-line-list {
  align-items: flex-start;
  border-radius: 0.31vw;
  display: flex;
  flex: 1;
  flex-grow: 1;
  gap: 14.53vw;
  min-width: 10.1vw;
  padding: 0.42vw;
  position: relative;
}

.x02-uebersicht .container-3 {
  align-items: flex-start;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.42vw;
  position: relative;
}

.x02-uebersicht .text-container {
  height: 2.34vw;
  position: relative;
  width: 7.71vw;
}

.x02-uebersicht .container-8 {
  align-items: center;
  display: flex;
  left: 0;
  padding: 0.0vw 20.0vw 0.0vw 0.0vw;
  position: absolute;
  top: 0;
  width: 6.88vw;
}

.x02-uebersicht .container-9 {
  align-items: center;
  display: flex;
  left: 0;
  padding: 0.0vw 20.0vw 0.0vw 0.0vw;
  position: absolute;
  top: 1.25vw;
  width: 6.88vw;
}

.x02-uebersicht .secondary-text {
  letter-spacing: 0;
  line-height: 1.25vw;
  margin-top: -0.05vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x02-uebersicht .frame-5 {
  align-items: flex-start;
  display: flex;
  flex: 1;
  flex-grow: 1;
  gap: 0.42vw;
  position: relative;
}

.x02-uebersicht .list-item-title-1 {
  letter-spacing: 0;
  line-height: 1.09vw;
  margin-right: -0.36vw;
  margin-top: -0.05vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x02-uebersicht .divider-1 {
  padding: 0.0vw 0.21vw;
}

.x02-uebersicht .line-1 {
  height: 0.05vw;
}

.x02-uebersicht .heading {
  align-items: center;
  display: inline-flex;
  height: 1.04vw;
  position: relative;
}

.x02-uebersicht .heading-1 {
  letter-spacing: 0.05vw;
  line-height: 0.88vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x02-uebersicht .container-10 {
  align-items: flex-start;
  display: flex;
  gap: 0.42vw;
  position: relative;
  width: 15.1vw;
}

.x02-uebersicht .secondary-text-1 {
  letter-spacing: 0;
  line-height: 1.25vw;
  margin-right: -1.3vw;
  margin-top: -0.05vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x02-uebersicht .heading-8 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  height: 1.04vw;
  position: relative;
}

.x02-uebersicht .heading-9 {
  letter-spacing: 0;
  line-height: 1.0vw;
  margin-top: -0.03vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x02-uebersicht .action-group-1 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.21vw;
  height: 1.25vw;
  justify-content: center;
  padding: 0.21vw 0.0vw;
  position: relative;
}

.x02-uebersicht .button-5 {
  align-items: center;
  border-radius: 0.31vw;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.42vw;
  height: 1.25vw;
  justify-content: center;
  margin-bottom: -0.21vw;
  margin-top: -0.21vw;
  padding: 0.62vw 0.83vw;
  position: relative;
}

.x02-uebersicht .button-6 {
  letter-spacing: 0;
  line-height: 1.0vw;
  margin-bottom: -0.44vw;
  margin-top: -0.55vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x02-uebersicht .page-content-item {
  align-items: flex-start;
  align-self: stretch;
  display: flex;
  flex: 1;
  flex-direction: column;
  flex-grow: 1;
  gap: 0.62vw;
  position: relative;
}

.x02-uebersicht .content-box {
  align-items: flex-start;
  align-self: stretch;
  border-radius: 0.42vw;
  display: flex;
  flex: 1;
  flex-direction: column;
  flex-grow: 1;
  position: relative;
  width: 100%;
}

.x02-uebersicht .tab-items {
  background-color: var(--white);
  border-radius: 0.42vw 0.0vw 0.0vw 0.0vw;
  display: inline-flex;
  gap: 0.21vw;
  height: 2.08vw;
  padding: 0.0vw 0.83vw;
}

.x02-uebersicht .tab-items-1 {
  display: inline-flex;
  flex: 0 0 auto;
}

.x02-uebersicht .tab-items-2 {
  border-radius: 0.0vw 0.42vw 0.0vw 0.0vw;
  display: flex;
  flex: 1;
  flex-grow: 1;
}

.x02-uebersicht .container-4 {
  align-items: flex-start;
  align-self: stretch;
  background-color: var(--white);
  border-radius: 0.0vw 0.0vw 0.31vw 0.31vw;
  display: flex;
  flex: 1;
  flex-direction: column;
  flex-grow: 1;
  gap: 0.83vw;
  padding: 0.83vw;
  position: relative;
  width: 100%;
}

.x02-uebersicht .bersicht {
  align-items: flex-start;
  align-self: stretch;
  background-color: var(--white);
  display: flex;
  flex: 1;
  flex-direction: column;
  flex-grow: 1;
  gap: 0.42vw;
  padding: 0.0vw 0.0vw 4.48vw;
  position: relative;
  width: 100%;
}

.x02-uebersicht .card {
  align-items: flex-start;
  background-color: var(--white);
  border: 0.05vw solid;
  border-color: var(--mercury);
  border-radius: 0.42vw;
  display: flex;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 0.21vw;
  overflow: hidden;
  padding: 0.62vw;
  position: relative;
  width: 30.42vw;
}

.x02-uebersicht .container-5 {
  align-items: flex-start;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  gap: 0.83vw;
  position: relative;
  width: 100%;
}

.x02-uebersicht .text {
  align-items: flex-start;
  display: flex;
  flex: 1;
  flex-direction: column;
  flex-grow: 1;
  gap: 0.21vw;
  position: relative;
}

.x02-uebersicht .heading-2 {
  align-items: center;
  align-self: stretch;
  display: flex;
  height: 1.04vw;
  position: relative;
  width: 100%;
}

.x02-uebersicht .heading-3 {
  flex: 1;
  letter-spacing: 0;
  line-height: 1.0vw;
  margin-top: -0.03vw;
  position: relative;
}

.x02-uebersicht .frame-1285 {
  align-items: center;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  justify-content: space-between;
  position: relative;
  width: 100%;
}

.x02-uebersicht .frame-1288 {
  align-items: center;
  display: flex;
  gap: 0.42vw;
  position: relative;
  width: 15.26vw;
}

.x02-uebersicht .badge {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.31vw;
  position: relative;
}

.x02-uebersicht .frame-683 {
  align-items: center;
  background-color: var(--magenta);
  border-radius: 0.62vw;
  display: flex;
  flex-direction: column;
  gap: 0.39vw;
  height: 1.25vw;
  justify-content: center;
  padding: 0.31vw 0.23vw;
  position: relative;
  width: 1.25vw;
}

.x02-uebersicht .mp {
  letter-spacing: 0;
  line-height: 0.62vw;
  margin-left: -0.03vw;
  margin-right: -0.03vw;
  margin-top: -0.01vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x02-uebersicht .button-2 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 0.21vw;
  height: 1.67vw;
  justify-content: center;
  padding: 0.62vw 0.21vw;
  position: relative;
}

.x02-uebersicht .label-16 {
  letter-spacing: 0;
  line-height: 1.0vw;
  margin-top: -0.03vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x02-uebersicht .label-18 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  height: 1.04vw;
  position: relative;
}

.x02-uebersicht .label-19 {
  letter-spacing: 0;
  line-height: 1.0vw;
  margin-top: -0.03vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x02-uebersicht .label-20 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  height: 1.04vw;
  position: relative;
}

.x02-uebersicht .label-21 {
  letter-spacing: 0;
  line-height: 1.0vw;
  margin-top: -0.03vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x02-uebersicht .label-22 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  height: 1.04vw;
  position: relative;
}

.x02-uebersicht .label-23 {
  letter-spacing: 0;
  line-height: 1.0vw;
  margin-top: -0.03vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x02-uebersicht .card-image {
  align-items: center;
  align-self: stretch;
  background-color: var(--white);
  border: 0.05vw solid;
  border-color: var(--mercury);
  border-radius: 0.42vw;
  display: flex;
  flex: 0 0 auto;
  gap: 0.62vw;
  padding: 0.62vw;
  position: relative;
  width: 100%;
}

.x02-uebersicht .frame-1287 {
  align-items: center;
  display: flex;
  flex: 1;
  flex-grow: 1;
  gap: 0.83vw;
  justify-content: center;
  position: relative;
}

.x02-uebersicht .frame-1286 {
  align-items: flex-start;
  display: flex;
  flex: 1;
  flex-direction: column;
  flex-grow: 1;
  position: relative;
}

.x02-uebersicht .frame-1289 {
  align-items: flex-start;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 0.52vw;
  position: relative;
  width: 100%;
}

.x02-uebersicht .paragraph-2 {
  align-items: center;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  position: relative;
  width: 100%;
}

.x02-uebersicht .rectangle-922-1 {
  height: 2.92vw;
  position: relative;
  width: 3.75vw;
}

.x02-uebersicht .content-box-1 {
  width: 100%;
}

.x02-uebersicht .heading-10 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  height: 1.04vw;
  position: relative;
}

.x02-uebersicht .heading-11 {
  letter-spacing: 0;
  line-height: 1.0vw;
  margin-top: -0.03vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x02-uebersicht .frame-683-1 {
  background-color: #92a5d3;
  border-radius: 0.62vw;
  gap: 0.39vw;
  height: 1.25vw;
  padding: 0.31vw 0.23vw;
  width: 1.25vw;
}

.x02-uebersicht .place {
  letter-spacing: 0;
  line-height: 0.62vw;
  margin-top: -0.01vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x02-uebersicht .label-24 {
  letter-spacing: 0;
  line-height: 1.0vw;
  margin-top: -0.03vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x02-uebersicht .label-25 {
  letter-spacing: 0;
  line-height: 1.0vw;
  margin-top: -0.03vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x02-uebersicht .heading-12 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  height: 1.04vw;
  position: relative;
}

.x02-uebersicht .heading-13 {
  letter-spacing: 0;
  line-height: 1.0vw;
  margin-top: -0.03vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x02-uebersicht .frame-683-3 {
  background-color: #fed27a;
  border-radius: 0.62vw;
  gap: 0.39vw;
  height: 1.25vw;
  padding: 0.31vw 0.23vw;
  width: 1.25vw;
}

.x02-uebersicht .tb-1 {
  letter-spacing: 0;
  line-height: 0.62vw;
  margin-top: -0.01vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x02-uebersicht .label-26 {
  letter-spacing: 0;
  line-height: 1.0vw;
  margin-top: -0.03vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x02-uebersicht .line-2 {
  align-self: stretch;
}

.x02-uebersicht .place-1 {
  letter-spacing: 0;
  line-height: 0.62vw;
  margin-top: -0.01vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x02-uebersicht .label-27 {
  letter-spacing: 0;
  line-height: 1.0vw;
  margin-top: -0.03vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x02-uebersicht .area-header-3 {
  align-items: center;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  justify-content: space-between;
  position: relative;
  width: 100%;
}

.x02-uebersicht .content-box-3 {
  align-items: flex-start;
  align-self: stretch;
  background-color: var(--white);
  border-radius: 0.42vw;
  display: flex;
  flex: 1;
  flex-direction: column;
  flex-grow: 1;
  gap: 0.83vw;
  padding: 0.83vw;
  position: relative;
}

.x02-uebersicht .divider-3 {
  align-items: center;
  align-self: stretch;
  display: flex;
  gap: 0.52vw;
  height: 0.83vw;
  justify-content: center;
  position: relative;
  width: 100%;
}

.x02-uebersicht .frame-1285-6 {
  align-items: center;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  justify-content: space-between;
  position: relative;
  width: 100%;
}

.x02-uebersicht .frame-3-6 {
  align-items: center;
  display: flex;
  left: 0;
  padding: 0.0vw 20.0vw 0.0vw 0.0vw;
  position: absolute;
  top: 0;
  width: 6.88vw;
}

.x02-uebersicht .frame-4-6 {
  align-items: center;
  display: flex;
  left: 0;
  padding: 0.0vw 20.0vw 0.0vw 0.0vw;
  position: absolute;
  top: 1.25vw;
  width: 6.88vw;
}

.x02-uebersicht .frame-683-4 {
  align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
}

.x02-uebersicht .header-4 {
  align-items: center;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  justify-content: space-between;
  position: relative;
  width: 100%;
}

.x02-uebersicht .line-3 {
  flex: 1;
  flex-grow: 1;
  object-fit: cover;
  position: relative;
}

.x02-uebersicht .mp-3 {
  letter-spacing: 0;
  line-height: 0.62vw;
  margin-left: -0.03vw;
  margin-right: -0.03vw;
  margin-top: -0.01vw;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.x02-uebersicht .tab-items-3 {
  align-items: center;
  background-color: var(--mercury);
  gap: 0.21vw;
  height: 2.08vw;
  padding: 0.0vw 0.83vw;
  position: relative;
}
