object DMMain: TDMMain
  OnCreate = WebDataModuleCreate
  Height = 317
  Width = 476
  object XDataConnection: TXDataWebConnection
    URL = 'http://localhost/evident/api/v1'
    OnConnect = XDataConnectionConnect
    OnError = XDataConnectionError
    Left = 72
    Top = 32
  end
  object QPatienten: TXDataWebDataSet
    EntitySetName = 'Patient'
    Connection = XDataConnection
    QueryString = 'NACHNAME ilike '#39'MONIEN'#39
    QueryTop = 50
    Left = 72
    Top = 128
    object QPatientenPatnr: TIntegerField
      FieldName = 'Patnr'
      Required = True
    end
    object QPatientenKasseId: TIntegerField
      FieldName = 'KasseId'
    end
    object QPatientenKassekz: TStringField
      FieldName = 'Kassekz'
      Size = 2
    end
    object QPatientenArbgeb: TStringField
      FieldName = 'Arbgeb'
      Size = 40
    end
    object QPatientenTelarbgeb: TStringField
      FieldName = 'Telarbgeb'
    end
    object QPatientenVersadr: TIntegerField
      FieldName = 'Versadr'
    end
    object QPatientenAbwadr: TIntegerField
      FieldName = 'Abwadr'
    end
    object QPatientenHausarztadr: TIntegerField
      FieldName = 'Hausarztadr'
    end
    object QPatientenUeberweiseradr: TIntegerField
      FieldName = 'Ueberweiseradr'
    end
    object QPatientenNachrichtl1adr: TIntegerField
      FieldName = 'Nachrichtl1adr'
    end
    object QPatientenNachrichtl2adr: TIntegerField
      FieldName = 'Nachrichtl2adr'
    end
    object QPatientenMahnen: TIntegerField
      FieldName = 'Mahnen'
    end
    object QPatientenBankeinzug: TIntegerField
      FieldName = 'Bankeinzug'
    end
    object QPatientenProthzuschuss: TIntegerField
      FieldName = 'Prothzuschuss'
    end
    object QPatientenAnamnese: TIntegerField
      FieldName = 'Anamnese'
    end
    object QPatientenEinldat1: TDateTimeField
      FieldName = 'Einldat1'
    end
    object QPatientenEinldat2: TDateTimeField
      FieldName = 'Einldat2'
    end
    object QPatientenKfozuschuss: TIntegerField
      FieldName = 'Kfozuschuss'
    end
    object QPatientenFreiwVers: TIntegerField
      FieldName = 'FreiwVers'
    end
    object QPatientenVersnr: TStringField
      FieldName = 'Versnr'
      Size = 70
    end
    object QPatientenKennz: TIntegerField
      FieldName = 'Kennz'
    end
    object QPatientenGlobPatnr: TIntegerField
      FieldName = 'GlobPatnr'
    end
    object QPatientenGebdat: TDateTimeField
      FieldName = 'Gebdat'
    end
    object QPatientenGeschlecht: TStringField
      FieldName = 'Geschlecht'
      Size = 1
    end
    object QPatientenTitel: TStringField
      FieldName = 'Titel'
      Size = 30
    end
    object QPatientenAnrede: TStringField
      FieldName = 'Anrede'
      Size = 30
    end
    object QPatientenBriefanrede: TStringField
      FieldName = 'Briefanrede'
      Size = 100
    end
    object QPatientenNamenszusatz: TStringField
      FieldName = 'Namenszusatz'
      Size = 41
    end
    object QPatientenNachname: TStringField
      FieldName = 'Nachname'
      Size = 45
    end
    object QPatientenVorname: TStringField
      FieldName = 'Vorname'
      Size = 45
    end
    object QPatientenSoundexnachname: TStringField
      FieldName = 'Soundexnachname'
      Size = 45
    end
    object QPatientenSoundexvorname: TStringField
      FieldName = 'Soundexvorname'
      Size = 45
    end
    object QPatientenStrasse: TStringField
      FieldName = 'Strasse'
      Size = 56
    end
    object QPatientenLkz: TStringField
      FieldName = 'Lkz'
      Size = 3
    end
    object QPatientenPlz: TStringField
      FieldName = 'Plz'
      Size = 10
    end
    object QPatientenOrt: TStringField
      FieldName = 'Ort'
      Size = 40
    end
    object QPatientenPostfach: TStringField
      FieldName = 'Postfach'
      Size = 12
    end
    object QPatientenPsfPlz: TStringField
      FieldName = 'PsfPlz'
      Size = 10
    end
    object QPatientenPsfOrt: TStringField
      FieldName = 'PsfOrt'
      Size = 40
    end
    object QPatientenTelefon1: TStringField
      FieldName = 'Telefon1'
    end
    object QPatientenTelefon2: TStringField
      FieldName = 'Telefon2'
    end
    object QPatientenFax: TStringField
      FieldName = 'Fax'
    end
    object QPatientenEmail: TStringField
      FieldName = 'Email'
      Size = 60
    end
    object QPatientenBeruf: TStringField
      FieldName = 'Beruf'
      Size = 30
    end
    object QPatientenDuzfreund: TStringField
      FieldName = 'Duzfreund'
      Size = 1
    end
    object QPatientenAnzahlung: TFloatField
      FieldName = 'Anzahlung'
    end
    object QPatientenStammbehandler: TStringField
      FieldName = 'Stammbehandler'
      Size = 3
    end
    object QPatientenAufnahmedatum: TDateTimeField
      FieldName = 'Aufnahmedatum'
    end
    object QPatientenRechbankkonto: TIntegerField
      FieldName = 'Rechbankkonto'
    end
    object QPatientenArchiviert: TStringField
      FieldName = 'Archiviert'
      Size = 1
    end
    object QPatientenArchivgesperrt: TStringField
      FieldName = 'Archivgesperrt'
      Size = 1
    end
    object QPatientenUebernahme: TStringField
      FieldName = 'Uebernahme'
      Size = 1
    end
    object QPatientenArchiviertam: TDateTimeField
      FieldName = 'Archiviertam'
    end
    object QPatientenArchiviertbenutzer: TStringField
      FieldName = 'Archiviertbenutzer'
      Size = 100
    end
    object QPatientenKontaktart: TIntegerField
      FieldName = 'Kontaktart'
    end
    object QPatientenVerstorben: TStringField
      FieldName = 'Verstorben'
      Size = 1
    end
    object QPatientenSms1: TStringField
      FieldName = 'Sms1'
    end
    object QPatientenGebort: TStringField
      FieldName = 'Gebort'
      Size = 30
    end
    object QPatientenGebname: TStringField
      FieldName = 'Gebname'
      Size = 30
    end
    object QPatientenVddsrzDefault: TStringField
      FieldName = 'VddsrzDefault'
      Size = 1
    end
    object QPatientenPrenotification: TIntegerField
      FieldName = 'Prenotification'
    end
    object QPatientenSperreTis: TStringField
      FieldName = 'SperreTis'
      Size = 1
    end
    object QPatientenSperreEmail: TStringField
      FieldName = 'SperreEmail'
      Size = 1
    end
    object QPatientenSperreSms: TStringField
      FieldName = 'SperreSms'
      Size = 1
    end
    object QPatientenSperrePatinfo: TStringField
      FieldName = 'SperrePatinfo'
      Size = 1
    end
    object QPatientenHatBeihilfe: TStringField
      FieldName = 'HatBeihilfe'
      Size = 1
    end
    object QPatientenZuzahlungsbefreit: TStringField
      FieldName = 'Zuzahlungsbefreit'
      Size = 1
    end
    object QPatientenZuzahlungsbefreitDatum: TDateTimeField
      FieldName = 'ZuzahlungsbefreitDatum'
    end
    object QPatientenNamenszusatzEgk: TStringField
      FieldName = 'NamenszusatzEgk'
      Size = 30
    end
    object QPatientenVorsatzwort: TStringField
      FieldName = 'Vorsatzwort'
    end
    object QPatientenStrasseEgk: TStringField
      FieldName = 'StrasseEgk'
      Size = 46
    end
    object QPatientenHausnummer: TStringField
      FieldName = 'Hausnummer'
      Size = 9
    end
    object QPatientenAnschriftenzusatz: TStringField
      FieldName = 'Anschriftenzusatz'
      Size = 40
    end
    object QPatientenLkzEgk: TStringField
      FieldName = 'LkzEgk'
      Size = 3
    end
    object QPatientenPlzEgk: TStringField
      FieldName = 'PlzEgk'
      Size = 10
    end
    object QPatientenOrtEgk: TStringField
      FieldName = 'OrtEgk'
      Size = 40
    end
    object QPatientenPsfLkz: TStringField
      FieldName = 'PsfLkz'
      Size = 3
    end
    object QPatientenInfektionsrisiko: TStringField
      FieldName = 'Infektionsrisiko'
      Size = 1
    end
    object QPatientenTisRemind: TStringField
      FieldName = 'TisRemind'
      Size = 1
    end
    object QPatientenTisRemindSms: TStringField
      FieldName = 'TisRemindSms'
      Size = 1
    end
    object QPatientenTisRemindEmail: TStringField
      FieldName = 'TisRemindEmail'
      Size = 1
    end
    object QPatientenTisRemindSmsVers: TStringField
      FieldName = 'TisRemindSmsVers'
      Size = 1
    end
    object QPatientenTisRemindEmailVers: TStringField
      FieldName = 'TisRemindEmailVers'
      Size = 1
    end
    object QPatientenTisRemindSmsRech: TStringField
      FieldName = 'TisRemindSmsRech'
      Size = 1
    end
    object QPatientenTisRemindEmailRech: TStringField
      FieldName = 'TisRemindEmailRech'
      Size = 1
    end
    object QPatientenTisRemindTyp: TIntegerField
      FieldName = 'TisRemindTyp'
    end
    object QPatientenProthzussystem: TIntegerField
      FieldName = 'Prothzussystem'
    end
    object QPatientenKeinrecall: TStringField
      FieldName = 'Keinrecall'
      Required = True
      Size = 1
    end
    object QPatientenHomecommunityid: TStringField
      FieldName = 'Homecommunityid'
      Size = 72
    end
    object QPatientenEparechtebis: TDateTimeField
      FieldName = 'Eparechtebis'
    end
    object QPatientenEparechtLeDocs: TStringField
      FieldName = 'EparechtLeDocs'
      Size = 1
    end
    object QPatientenEparechtKtrDocs: TStringField
      FieldName = 'EparechtKtrDocs'
      Size = 1
    end
    object QPatientenEparechtVersDocs: TStringField
      FieldName = 'EparechtVersDocs'
      Size = 1
    end
    object QPatientenEtiId: TLargeintField
      FieldName = 'EtiId'
    end
    object QPatientenEtiCreated: TDateTimeField
      FieldName = 'EtiCreated'
    end
    object QPatientenEtiModified: TDateTimeField
      FieldName = 'EtiModified'
    end
    object QPatientenListView: TStringField
      FieldKind = fkCalculated
      FieldName = 'ListView'
      Size = 1000
      Calculated = True
    end
  end
  object QEmailsForPatient: TXDataWebDataSet
    EntitySetName = 'Email'
    Connection = XDataConnection
    QueryString = 'Patient/PatNr eq 7'
    QueryTop = 10
    Left = 256
    Top = 128
    object QEmailsForPatientPatient: TXDataWebEntityField
      FieldName = 'Patient'
      Required = True
    end
    object QEmailsForPatientId: TLargeintField
      FieldName = 'Id'
      Required = True
    end
    object QEmailsForPatientMessageid: TBlobField
      FieldName = 'Messageid'
      Size = 128
    end
    object QEmailsForPatientTyp: TIntegerField
      FieldName = 'Typ'
      Required = True
    end
    object QEmailsForPatientGesendet: TDateTimeField
      FieldName = 'Gesendet'
      Required = True
    end
    object QEmailsForPatientBetreff: TStringField
      FieldName = 'Betreff'
      Required = True
      Size = 128
    end
    object QEmailsForPatientTitel: TStringField
      FieldName = 'Titel'
      Size = 128
    end
    object QEmailsForPatientNotiz: TBlobField
      FieldName = 'Notiz'
      Size = 80
    end
    object QEmailsForPatientWichtigkeit: TIntegerField
      FieldName = 'Wichtigkeit'
    end
    object QEmailsForPatientRichtung: TIntegerField
      FieldName = 'Richtung'
    end
  end
  object XDataWebClient: TXDataWebClient
    Connection = XDataConnection
    OnRequest = XDataWebClientRequest
    Left = 240
    Top = 32
  end
end
