﻿unit Second.Form;

interface

uses
  System.SysUtils, System.Classes, JS, Web, WEBLib.Graphics, WEBLib.Controls,
  WEBLib.Forms, WEBLib.Dialogs, Vcl.Controls, Vcl.StdCtrls, WEBLib.StdCtrls;

type
  TFormSecond = class(TWebForm)
    WebLabel1: TWebLabel;
  private
    { Private-Deklarationen }
  public
    { Public-Deklarationen }
  end;

var
  FormSecond: TFormSecond;

implementation

{$R *.dfm}

initialization
  RegisterClass(TFormSecond);
end.

