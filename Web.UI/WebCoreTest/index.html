﻿<!DOCTYPE html>
<html lang="de">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta content="width=device-width, initial-scale=1" name="viewport"/>
    <meta $(ThemeColor)/>
    <noscript>Your browser does not support JavaScript!</noscript>
    <link href="IconResMid.png" rel="icon" sizes="192x192"/>
    <link href="IconResMid.png" rel="apple-touch-icon"/>
    <script src="serviceworker.js" type="text/javascript"></script>
    <meta $(Manifest)/>
    <link $(FavIcon)/>
    <title>TMS Web Project</title>
    <script src="$(ProjectName).js" type="text/javascript"></script>
    <style>
     html, body {
          margin: 0;
          padding: 0;
          overflow: hidden; /* Keine Browser-Scrollbars */
          width: 100vw;
          height: 100vh;
          background-color: #ffffff;
          font-family: sans-serif;
        }

        #app-container {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100vh;                /* Höhe des Status Panels wird im OnCreate des Status Forms berechnet*/
          overflow: auto;               /* Scrollen innerhalb des Containers */
          box-sizing: border-box;
          padding: 0;                   /* Optional: oder 20px wie zuvor */
          display: block;               /* wichtig: kein Flex hier */
    }
    </style>
  <link href="main.css" rel="stylesheet"/></head>
  <body>
<meta $(BodyParameters)/>
  </body>
  <script type="text/javascript">rtl.run();</script>
</html>



