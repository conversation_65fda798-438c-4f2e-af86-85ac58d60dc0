﻿program WebCoreTest;

{$R *.dres}

uses
  Vcl.Forms,
  WEBLib.Forms,
  Main.Form in 'Main.Form.pas' {FormMain: TWebForm} {*.html},
  Second.Form in 'Second.Form.pas' {FormSecond: TWebForm} {*.html};

{$R *.res}

begin
  Application.Initialize;
  Application.MainFormOnTaskbar := True;
  Application.Routing := true;
  Application.AutoFormRoute := true;
  Application.CreateForm(TFormMain, FormMain);
  Application.CreateForm(TFormSecond, FormSecond);
  Application.Run;
end.
