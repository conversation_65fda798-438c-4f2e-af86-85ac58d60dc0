﻿unit Main.Form;

interface

uses
  System.SysUtils, System.Classes, JS, Web, WEBLib.Graphics, WEBLib.Controls,
  WEBLib.Forms, WEBLib.Dialogs, Vcl.Controls, WEBLib.ExtCtrls, Vcl.StdCtrls, WEBLib.StdCtrls;

type
  TFormMain = class(TWebForm)
    PanelStatus: TWebPanel;
    WebButton1: TWebButton;
    procedure WebButton1Click(Sender: TObject);
    procedure WebFormCreate(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
  end;

var
  FormMain: TFormMain;

implementation

{$R *.dfm}

procedure TFormMain.WebButton1Click(Sender: TObject);
var
  LForm: TWebForm;
begin

LForm := T.CreateNew(
    procedure(AForm: TObject)
    begin
      TWebForm(AForm).Popup := false;
      TWebForm(AForm).Border := fbSingle;
      if Assigned(AFormCreatedProc) then
      begin
        AFormCreatedProc(T(AForm));
      end;
      TWebForm(AForm).ShowModal;
    end);

end;

procedure TFormMain.WebFormCreate(Sender: TObject);
var
  LPanelHeight: Integer;
begin
  LPanelHeight := PanelStatus.Height;
  asm
  document.getElementById("app-container").style.height = "calc(100vh - " + LPanelHeight + "px)";
  end;
end;

end.

